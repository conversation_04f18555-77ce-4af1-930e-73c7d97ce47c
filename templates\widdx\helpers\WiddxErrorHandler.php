<?php
/**
 * WIDDX Enhanced Error Handler
 * Provides comprehensive error handling with security considerations
 */

class WiddxErrorHandler {
    private static $instance = null;
    private $logFile;
    private $isProduction;
    private $allowedErrorTypes;
    
    private function __construct() {
        $this->logFile = __DIR__ . '/../logs/widdx_errors.log';
        $this->isProduction = !defined('WHMCS_DEBUG') || !WHMCS_DEBUG;
        $this->allowedErrorTypes = [
            E_ERROR,
            E_WARNING,
            E_PARSE,
            E_NOTICE,
            E_CORE_ERROR,
            E_CORE_WARNING,
            E_COMPILE_ERROR,
            E_COMPILE_WARNING,
            E_USER_ERROR,
            E_USER_WARNING,
            E_USER_NOTICE,
            E_RECOVERABLE_ERROR
        ];
        
        $this->ensureLogDirectory();
        $this->registerHandlers();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Create .htaccess to protect log files
        $htaccessFile = $logDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    private function registerHandlers() {
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
        register_shutdown_function([$this, 'handleShutdown']);
    }
    
    public function handleError($severity, $message, $file, $line) {
        // Don't handle errors that are not in our allowed types
        if (!in_array($severity, $this->allowedErrorTypes)) {
            return false;
        }
        
        // Don't handle suppressed errors (with @)
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorData = [
            'type' => 'PHP Error',
            'severity' => $this->getSeverityName($severity),
            'message' => $message,
            'file' => $this->sanitizeFilePath($file),
            'line' => $line,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $this->getClientIP(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI'
        ];
        
        $this->logError($errorData);
        
        // In production, don't show detailed errors to users
        if ($this->isProduction) {
            $this->showUserFriendlyError();
        }
        
        return true;
    }
    
    public function handleException($exception) {
        $errorData = [
            'type' => 'PHP Exception',
            'severity' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $this->sanitizeFilePath($exception->getFile()),
            'line' => $exception->getLine(),
            'trace' => $this->sanitizeStackTrace($exception->getTraceAsString()),
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $this->getClientIP(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI'
        ];
        
        $this->logError($errorData);
        
        if ($this->isProduction) {
            $this->showUserFriendlyError();
        } else {
            $this->showDetailedError($errorData);
        }
    }
    
    public function handleShutdown() {
        $error = error_get_last();
        
        if ($error !== null && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
            $errorData = [
                'type' => 'PHP Fatal Error',
                'severity' => $this->getSeverityName($error['type']),
                'message' => $error['message'],
                'file' => $this->sanitizeFilePath($error['file']),
                'line' => $error['line'],
                'timestamp' => date('Y-m-d H:i:s'),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                'ip' => $this->getClientIP(),
                'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI'
            ];
            
            $this->logError($errorData);
            
            if ($this->isProduction) {
                $this->showUserFriendlyError();
            }
        }
    }
    
    private function logError($errorData) {
        $logEntry = sprintf(
            "[%s] %s: %s in %s:%d | IP: %s | URL: %s | UA: %s\n",
            $errorData['timestamp'],
            $errorData['severity'],
            $errorData['message'],
            $errorData['file'],
            $errorData['line'],
            $errorData['ip'],
            $errorData['url'],
            substr($errorData['user_agent'], 0, 100)
        );
        
        if (isset($errorData['trace'])) {
            $logEntry .= "Stack Trace:\n" . $errorData['trace'] . "\n";
        }
        
        $logEntry .= str_repeat('-', 80) . "\n";
        
        // Use file locking to prevent race conditions
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        
        // Rotate log file if it gets too large (10MB)
        if (file_exists($this->logFile) && filesize($this->logFile) > 10 * 1024 * 1024) {
            $this->rotateLogFile();
        }
    }
    
    private function rotateLogFile() {
        $backupFile = $this->logFile . '.' . date('Y-m-d-H-i-s') . '.bak';
        rename($this->logFile, $backupFile);
        
        // Keep only last 5 backup files
        $logDir = dirname($this->logFile);
        $backupFiles = glob($logDir . '/*.bak');
        if (count($backupFiles) > 5) {
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            for ($i = 0; $i < count($backupFiles) - 5; $i++) {
                unlink($backupFiles[$i]);
            }
        }
    }
    
    private function showUserFriendlyError() {
        if (!headers_sent()) {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
        }
        
        echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Temporarily Unavailable</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 50px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }
        .icon { font-size: 64px; color: #e74c3c; margin-bottom: 20px; }
        h1 { color: #2c3e50; margin-bottom: 20px; }
        p { color: #7f8c8d; line-height: 1.6; margin-bottom: 30px; }
        .btn { display: inline-block; padding: 12px 24px; background: #3498db; color: white; text-decoration: none; border-radius: 4px; }
        .btn:hover { background: #2980b9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">⚠️</div>
        <h1>Service Temporarily Unavailable</h1>
        <p>We\'re experiencing technical difficulties. Our team has been notified and is working to resolve the issue.</p>
        <p>Please try again in a few minutes.</p>
        <a href="javascript:history.back()" class="btn">Go Back</a>
    </div>
</body>
</html>';
        exit;
    }
    
    private function showDetailedError($errorData) {
        if (!headers_sent()) {
            http_response_code(500);
            header('Content-Type: text/html; charset=UTF-8');
        }
        
        echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Details</title>
    <style>
        body { font-family: monospace; margin: 20px; background: #1e1e1e; color: #fff; }
        .error-container { background: #2d2d2d; padding: 20px; border-radius: 8px; border-left: 4px solid #e74c3c; }
        .error-type { color: #e74c3c; font-weight: bold; font-size: 18px; }
        .error-message { color: #f39c12; margin: 10px 0; }
        .error-location { color: #3498db; }
        .error-trace { background: #1a1a1a; padding: 15px; margin-top: 15px; border-radius: 4px; overflow-x: auto; }
        .error-meta { margin-top: 15px; padding-top: 15px; border-top: 1px solid #444; color: #bbb; }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-type">' . htmlspecialchars($errorData['type']) . ': ' . htmlspecialchars($errorData['severity']) . '</div>
        <div class="error-message">' . htmlspecialchars($errorData['message']) . '</div>
        <div class="error-location">File: ' . htmlspecialchars($errorData['file']) . ' | Line: ' . $errorData['line'] . '</div>';
        
        if (isset($errorData['trace'])) {
            echo '<div class="error-trace"><strong>Stack Trace:</strong><br>' . nl2br(htmlspecialchars($errorData['trace'])) . '</div>';
        }
        
        echo '<div class="error-meta">
            <strong>Timestamp:</strong> ' . $errorData['timestamp'] . '<br>
            <strong>URL:</strong> ' . htmlspecialchars($errorData['url']) . '<br>
            <strong>Method:</strong> ' . htmlspecialchars($errorData['method']) . '<br>
            <strong>IP:</strong> ' . htmlspecialchars($errorData['ip']) . '
        </div>
    </div>
</body>
</html>';
        exit;
    }
    
    private function getSeverityName($severity) {
        $severityNames = [
            E_ERROR => 'Fatal Error',
            E_WARNING => 'Warning',
            E_PARSE => 'Parse Error',
            E_NOTICE => 'Notice',
            E_CORE_ERROR => 'Core Error',
            E_CORE_WARNING => 'Core Warning',
            E_COMPILE_ERROR => 'Compile Error',
            E_COMPILE_WARNING => 'Compile Warning',
            E_USER_ERROR => 'User Error',
            E_USER_WARNING => 'User Warning',
            E_USER_NOTICE => 'User Notice',
            E_RECOVERABLE_ERROR => 'Recoverable Error'
        ];
        
        return $severityNames[$severity] ?? 'Unknown Error';
    }
    
    private function sanitizeFilePath($file) {
        // Remove sensitive path information
        $documentRoot = $_SERVER['DOCUMENT_ROOT'] ?? '';
        if ($documentRoot && strpos($file, $documentRoot) === 0) {
            return substr($file, strlen($documentRoot));
        }
        
        // Remove absolute paths for security
        return basename(dirname($file)) . '/' . basename($file);
    }
    
    private function sanitizeStackTrace($trace) {
        // Remove sensitive information from stack trace
        $lines = explode("\n", $trace);
        $sanitized = [];
        
        foreach ($lines as $line) {
            // Remove absolute paths
            $line = preg_replace('/\/[^\/\s]*\/[^\/\s]*\/[^\/\s]*\//', '.../', $line);
            $sanitized[] = $line;
        }
        
        return implode("\n", $sanitized);
    }
    
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    }
    
    public static function logCustomError($message, $context = []) {
        $instance = self::getInstance();
        
        $errorData = [
            'type' => 'Custom Error',
            'severity' => 'Application',
            'message' => $message,
            'file' => 'N/A',
            'line' => 0,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $instance->getClientIP(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
            'context' => json_encode($context)
        ];
        
        $instance->logError($errorData);
    }
    
    public static function logSecurityEvent($event, $details = []) {
        $instance = self::getInstance();
        
        $securityData = [
            'type' => 'Security Event',
            'severity' => 'Security',
            'message' => $event,
            'file' => 'Security Monitor',
            'line' => 0,
            'timestamp' => date('Y-m-d H:i:s'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip' => $instance->getClientIP(),
            'url' => $_SERVER['REQUEST_URI'] ?? 'CLI',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
            'details' => json_encode($details)
        ];
        
        $instance->logError($securityData);
    }
}

// Initialize error handler
WiddxErrorHandler::getInstance();