<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:15
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\inc\widdx-navbar.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c874ab277_17864899',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '4fd2be39999109f6e4e752d33e67489f4a1e8ddc' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\inc\\widdx-navbar.tpl',
      1 => 1747856371,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c874ab277_17864899 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'C:\\xampp\\htdocs\\Whmcs\\vendor\\smarty\\smarty\\libs\\plugins\\modifier.replace.php','function'=>'smarty_modifier_replace',),));
$_smarty_tpl->_assignInScope('menuIcons', array('dashboard'=>'fas fa-tachometer-alt','services'=>'fas fa-server','domains'=>'fas fa-globe','billing'=>'fas fa-file-invoice-dollar','support'=>'fas fa-headset','tickets'=>'fas fa-ticket-alt','store'=>'fas fa-shopping-cart','security'=>'fas fa-shield-alt','website security'=>'fas fa-shield-alt','email'=>'fas fa-envelope','ssl'=>'fas fa-lock','website'=>'fas fa-desktop','database'=>'fas fa-database','backup'=>'fas fa-backup','settings'=>'fas fa-cog','account'=>'fas fa-user-circle','profile'=>'fas fa-user','contacts'=>'fas fa-users','reports'=>'fas fa-chart-bar','tools'=>'fas fa-tools','home'=>'fas fa-home','announcements'=>'fas fa-bullhorn','knowledgebase'=>'fas fa-book','downloads'=>'fas fa-download','network'=>'fas fa-network-wired','affiliates'=>'fas fa-users','marketplace'=>'fas fa-shopping-basket','orders'=>'fas fa-shopping-bag','open ticket'=>'fas fa-ticket'));?>

<ul class="menu-inner py-1">
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['navbar']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?>
        <li class="menu-item" menuItemName="<?php echo $_smarty_tpl->tpl_vars['item']->value->getName();?>
" id="<?php echo $_smarty_tpl->tpl_vars['item']->value->getId();?>
">
            <a <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?> class="menu-link menu-toggle<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> rtl-toggle<?php }?>" data-toggle="dropdown" aria-haspopup="true"
                aria-expanded="false" <?php } else { ?> class="menu-link" href="<?php echo $_smarty_tpl->tpl_vars['item']->value->getUri();?>
"
                    <?php }
if ($_smarty_tpl->tpl_vars['item']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['item']->value->getAttribute('target');?>
" <?php }
if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="text-align: right;"<?php }?>>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->hasIcon()) {?>
                        <i class="menu-icon <?php echo $_smarty_tpl->tpl_vars['item']->value->getIcon();?>
"<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="margin-right: 0px; margin-left: 1rem;"<?php }?>></i>
                    <?php } else { ?>
                        <?php $_smarty_tpl->_assignInScope('itemName', smarty_modifier_replace(mb_strtolower($_smarty_tpl->tpl_vars['item']->value->getName(), 'UTF-8'),'_',' '));?>
                        <?php if ((isset($_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value]))) {?>
                            <i class="menu-icon <?php echo $_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value];?>
"<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="margin-right: 0px; margin-left: 1rem;"<?php }?>></i>
                        <?php }?>
                    <?php }?>
                    <div class="menu-text"><?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>
</div>
                    <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBadge()) {?><span class="badge badge-center rounded-pill bg-primary <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php }?>
                </a>
                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
                    <ul class="menu-sub"<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="padding-right: 1.5rem; padding-left: 0px;"<?php }?>>
                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                            <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass() && in_array($_smarty_tpl->tpl_vars['childItem']->value->getClass(),array('dropdown-divider','nav-divider'))) {?>
                                <div class="dropdown-divider"></div>
                            <?php } else { ?>
                                <li class="menu-item" menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
" id="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getId();?>
">
                                    <a class="menu-link" href="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
" <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target')) {?>
                                        target="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target');?>
" <?php }
if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="text-align: right;"<?php }?>>
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?>
                                            <i class="menu-icon <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
"<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="margin-right: 0px; margin-left: 1rem;"<?php }?>></i>
                                        <?php } else { ?>
                                            <?php $_smarty_tpl->_assignInScope('itemName', smarty_modifier_replace(mb_strtolower($_smarty_tpl->tpl_vars['childItem']->value->getName(), 'UTF-8'),'_',' '));?>
                                            <?php if ((isset($_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value]))) {?>
                                                <i class="menu-icon <?php echo $_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value];?>
"<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?> style="margin-right: 0px; margin-left: 1rem;"<?php }?>></i>
                                            <?php }?>
                                        <?php }?>
                                        <div class="menu-text"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>
</div>
                                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?><span class="badge badge-center rounded-pill bg-primary <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span><?php }?>
                                    </a>
                                </li>
                            <?php }?>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </ul>
                <?php }?>
            </li>
        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    </ul><?php }
}
