<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:12:32
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\seo-s\widdx-basic-seo.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0d10693d90_49738152',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'c77453c471743b6d818bc577eec1b697ec08e726' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\seo-s\\widdx-basic-seo.tpl',
      1 => 1749349593,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0d10693d90_49738152 (Smarty_Internal_Template $_smarty_tpl) {
?><section id="basic-seo" style="padding: 20px; margin-bottom: 20px; border: 1px solid #ddd; background: #fff;">
    <h4>Basic SEO</h4>
    <p>This involves optimizing fundamental elements that affect the website's visibility in search engines. It
        includes:</p>
    <div class="row align-items-center">
        <div class="col-md-12 col-lg-12">
        
            <div class="accordion" id="accordionwiddxbasic">

                <!-- SEO Title -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            <i class="fas
                                <?php if (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) > 60 || strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) < 30) {?>
                                fa-times-circle text-danger
                                <?php } elseif (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) >= 30 && strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) <= 60) {?>
                                fa-check-circle text-success
                                <?php } else { ?>
                                fa-exclamation-circle text-warning
                                <?php }?> mr-2"></i>SEO Title
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse-basic collapse show" aria-labelledby="headingOne"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title'];?>
</div>
                            <?php if (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) > 60) {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> SEO title is too
                                long. Shorten it to 60 characters or fewer.</p>
                            <p class="text-danger">A concise title helps ensure it displays fully in search results and
                                improves user engagement. Make it both descriptive and engaging.</p>
                            <?php } elseif (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['title']) < 30) {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> SEO title is too
                                short. Lengthen it to between 30-60 characters.</p>
                            <p class="text-danger">A longer title with relevant keywords can enhance search visibility
                                and click-through rates. Ensure it reads naturally and is compelling.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> SEO title length is
                                good. Ensure it includes your target keywords and encourages clicks.</p>
                            <p class="text-success">Your title is well-optimized. It should clearly reflect the page
                                content and include relevant keywords.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Meta Description -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            <i class="fas
                                <?php if (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) > 160 || strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) < 50) {?>
                                fa-times-circle text-danger
                                <?php } elseif (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) >= 50 && strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) <= 160) {?>
                                fa-check-circle text-success
                                <?php } else { ?>
                                fa-exclamation-circle text-warning
                                <?php }?> mr-2"></i>Meta Description
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse-basic collapse" aria-labelledby="headingTwo"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription'];?>
</div>
                            <?php if (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) > 160) {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Meta description is
                                too long. Shorten it to 160 characters or fewer.</p>
                            <p class="text-danger">A well-crafted meta description can boost click-through rates. Make
                                it concise and include key terms that attract users.</p>
                            <?php } elseif (strlen($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['metaDescription']) < 50) {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Meta description is
                                too short. Lengthen it to between 50-160 characters.</p>
                            <p class="text-danger">A longer meta description provides a better summary and can entice
                                users to click. Aim for clarity and relevance.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> Meta description
                                length is good. Ensure it is compelling and includes keywords.</p>
                            <p class="text-success">Your meta description is well-optimized. It should accurately
                                summarize the page content and include key terms.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Keywords -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            <i class="fas
                              <?php if (empty($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['keywords'])) {?>
                              fa-times-circle text-danger
                              <?php } else { ?>
                              fa-check-circle text-success
                              <?php }?> mr-2"></i>Keywords
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse-basic collapse" aria-labelledby="headingThree"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['keywords'];?>
</div>
                            <?php if (empty($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['keywords'])) {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Keywords are missing.
                                Consider adding relevant keywords.</p>
                            <p class="text-danger">Including keywords can assist with optimization, though it's less
                                crucial now. Focus on using relevant keywords naturally in your content.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> Keywords are
                                present. Use them strategically throughout your content.</p>
                            <p class="text-success">Your keywords are present. Ensure they are used naturally throughout
                                your content to enhance SEO without keyword stuffing.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- H1 Tags -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFour">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                            <i class="fas <?php if (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h1Tags']) == 0) {?> fa-times-circle text-danger <?php } elseif (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h1Tags']) > 1) {?> fa-exclamation-circle text-warning
                        <?php } else { ?>
                        fa-check-circle text-success
                        <?php }?> mr-2"></i>H1 Tags
                        </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse-basic collapse" aria-labelledby="headingFour"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h1Tags'], 'h1');
$_smarty_tpl->tpl_vars['h1']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['h1']->value) {
$_smarty_tpl->tpl_vars['h1']->do_else = false;
?>
                                    <p><?php echo $_smarty_tpl->tpl_vars['h1']->value;?>
</p>
                                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                            </div>
                            <?php if (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h1Tags']) == 0) {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No H1 tags found. Add
                                    an H1 tag to your page.</p>
                                <p class="text-danger">An H1 tag is important for content structure and SEO. It should
                                    describe the main topic of the page.</p>
                            <?php } elseif (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h1Tags']) > 1) {?>
                                <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Multiple H1
                                    tags found. Use only one H1 tag per page.</p>
                                <p class="text-warning">Multiple H1 tags can confuse search engines. Use one H1 tag for the
                                    main topic and H2/H3 tags for subtopics.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> H1 tag usage is
                                    good. Use H1 tags to define the main topic.</p>
                                <p class="text-success">Your use of H1 tags is appropriate. It helps organize content and
                                    makes it easier for search engines to understand.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- H2 Tags -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                            <i class="fas
                                <?php if (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h2Tags']) == 0) {?>
                                fa-times-circle text-danger
                                <?php } else { ?>
                                fa-check-circle text-success
                                <?php }?> mr-2"></i>H2 Tags
                        </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse-basic collapse" aria-labelledby="headingFive"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h2Tags'], 'h2');
$_smarty_tpl->tpl_vars['h2']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['h2']->value) {
$_smarty_tpl->tpl_vars['h2']->do_else = false;
?>
                                    <p><?php echo $_smarty_tpl->tpl_vars['h2']->value;?>
</p>
                                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                            </div>
                            <?php if (count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['h2Tags']) == 0) {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No H2 tags found. Add
                                    H2 tags to structure your content.</p>
                                <p class="text-danger">H2 tags help break up content and improve readability. Use them to
                                    organize sections logically.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> H2 tag usage is
                                    good. Use H2 tags to organize subsections.</p>
                                <p class="text-success">Your H2 tags are used effectively. They enhance content structure
                                    and improve user experience.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Image Alt Text -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingBasicSeoFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseBasicSeoFive" aria-expanded="false"
                            aria-controls="collapseBasicSeoFive">
                            <i class="fas
                                <?php if (empty($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['imagesWithoutAlt'])) {?>
                                fa-check-circle text-success
                                <?php } else { ?>
                                fa-times-circle text-danger
                                <?php }?> mr-2"></i>Image Alt Text
                        </button>
                    </h2>
                    <div id="collapseBasicSeoFive" class="accordion-collapse collapse"
                        aria-labelledby="headingBasicSeoFive" data-bs-parent="#accordionBasicSeo">
                        <div class="accordion-body">
                            <?php if (empty($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['imagesWithoutAlt'])) {?>
                                <p class="text-success">All images have alt text. Good job!</p>
                            <?php } else { ?>
                                <p class="text-danger">Some images are missing alt text. This can negatively impact your SEO
                                    and accessibility.</p>
                                <p>Images without alt text:</p>
                                <ul>
                                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['imagesWithoutAlt'], 'image');
$_smarty_tpl->tpl_vars['image']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['image']->value) {
$_smarty_tpl->tpl_vars['image']->do_else = false;
?>
                                        <li>
                                            <strong><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['image']->value['name'], ENT_QUOTES, 'UTF-8', true);?>
</strong>:
                                            <a href="<?php echo htmlspecialchars($_smarty_tpl->tpl_vars['image']->value['src'], ENT_QUOTES, 'UTF-8', true);?>
" target="_blank"><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['image']->value['src'], ENT_QUOTES, 'UTF-8', true);?>
</a>
                                        </li>
                                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                </ul>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Link Ratio -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSeven">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseSeven" aria-expanded="false" aria-controls="collapseSeven">
                            <i class="fas
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['internal'] == 0 || $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['external'] == 0) {?>
                            fa-times-circle text-danger
                            <?php } else { ?>
                            fa-check-circle text-success
                            <?php }?> mr-2"></i>Link Ratio
                        </button>
                    </h2>
                    <div id="collapseSeven" class="accordion-collapse-basic collapse" aria-labelledby="headingSeven"
                        data-bs-parent="#accordionwiddxbasic">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                Internal: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['internal'];?>
, External:
                                <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['external'];?>
, Total: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['total'];?>
,
                                Ratio: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['ratio'];?>

                            </div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['internal'] == 0) {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No internal links
                                    found. Add internal links to improve site structure.</p>
                                <p class="text-danger">Internal links help distribute page authority and enhance user
                                    navigation.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['linkRatio']['external'] == 0) {?>
                                <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> No external
                                    links found. Consider adding high-quality external links.</p>
                                <p class="text-warning">External links to authoritative sites can boost credibility and
                                    provide additional value.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> Link ratio is good.
                                    Maintain a healthy balance of internal and external links.</p>
                                <p class="text-success">Your link ratio is well-balanced. Continue managing internal and
                                    external links effectively to support SEO and user experience.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section><?php }
}
