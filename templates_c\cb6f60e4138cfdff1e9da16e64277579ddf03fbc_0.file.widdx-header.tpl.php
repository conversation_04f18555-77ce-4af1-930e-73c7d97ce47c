<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:17:09
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\inc\widdx-header.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0e25d2cb66_48730594',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'cb6f60e4138cfdff1e9da16e64277579ddf03fbc' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\inc\\widdx-header.tpl',
      1 => 1747703317,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0e25d2cb66_48730594 (Smarty_Internal_Template $_smarty_tpl) {
?><header id="header" class="header">

    <!-- Header for Large Screens -->
    <div class="navbar navbar-expand-xl d-none d-xl-flex">
        <div class="container d-flex justify-content-between">
            <div class="d-flex align-items-center">
                <!-- Brand Logo or Name -->
                <a class="navbar-brand mx-3" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/index.php">
                    <?php if ($_smarty_tpl->tpl_vars['assetLogoPath']->value) {?>
                        <img src="<?php echo $_smarty_tpl->tpl_vars['assetLogoPath']->value;?>
" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="logo-img">
                    <?php } else { ?>
                        <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>

                    <?php }?>
                </a>

                <!-- Search Icon Widget -->
                <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/widgets/widdx-search-icon.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

                <!-- Primary Navbar -->
                <ul class="navbar-nav">
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-navbar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('navbar'=>$_smarty_tpl->tpl_vars['primaryNavbar']->value), 0, true);
?>
                </ul>
            </div>

            <div class="d-flex align-items-center">
                <?php if ($_smarty_tpl->tpl_vars['languagechangeenabled']->value && count($_smarty_tpl->tpl_vars['locales']->value) > 1 || $_smarty_tpl->tpl_vars['currencies']->value) {?>
                    <li class="list-inline-item">
                        <button type="button" class="btn" data-toggle="modal" data-target="#modalChooseLanguage">
                            <div class="d-inline-block align-middle">
                                <div
                                    class="iti-flag <?php if ($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'] === 'GB') {?>us<?php } else {
echo mb_strtolower($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'], 'UTF-8');
}?>">
                                </div>
                            </div>
                            <?php echo $_smarty_tpl->tpl_vars['activeLocale']->value['localisedName'];?>

                            /
                            <?php echo $_smarty_tpl->tpl_vars['activeCurrency']->value['prefix'];?>

                            <?php echo $_smarty_tpl->tpl_vars['activeCurrency']->value['code'];?>

                        </button>
                    </li>
                <?php }?>
                <!-- Cart Button -->
                <a class="btn nav-link cart-btn mr-3" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=view">
                    <i class="fas fa-shopping-cart"></i>
                    <span id="cartItemCount" class="badge badge-info"><?php echo $_smarty_tpl->tpl_vars['cartitemcount']->value;?>
</span>
                    <span class="sr-only"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"carttitle"),$_smarty_tpl ) );?>
</span>
                </a>

                <!-- Theme Toggle Button -->
                <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-header-theme-switcher.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

                <!-- Secondary Navbar -->
                <ul class="navbar-nav">
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-navbar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('navbar'=>$_smarty_tpl->tpl_vars['secondaryNavbar']->value,'rightDrop'=>true), 0, true);
?>
                </ul>
            </div>
        </div>
    </div>


    <!-- Header for Small Screens -->
    <div class="navbar p-2 navbar-expand-xl d-xl-none">
        <div class="container">
            <!-- Brand Logo or Name -->
            <a class="navbar-brand mr-3" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/index.php">
                <?php if ($_smarty_tpl->tpl_vars['assetLogoPath']->value) {?>
                    <img src="<?php echo $_smarty_tpl->tpl_vars['assetLogoPath']->value;?>
" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="logo-img">
                <?php } else { ?>
                    <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>

                <?php }?>
            </a>

            <!-- Toggle Button for Sidebar -->
            <ul class="navbar-nav toolbar ml-auto">
                <!-- Theme Toggle Button for Mobile -->
                <li class="nav-item ml-3 d-xl-none">
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-header-theme-switcher.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                </li>
                <li class="nav-item ml-3 d-xl-none">
                    <button class="btn nav-link" type="button" data-toggle="collapse" data-target="#widdxSidebar">
                        <span class="fas fa-bars fa-fw"></span>
                    </button>
                </li>
            </ul>
        </div>
    </div>
</header>

<!-- Sidebar -->
<div class="widdxsidebar collapse" id="widdxSidebar">
    <div class="sidebar-header d-flex justify-content-between align-items-center p-3 border-bottom">
        <!-- Sidebar Header with Close Button -->
        <h5 class="mb-0">MENU</h5>
        <button type="button" class="close" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="sidebar-content p-3">
        <!-- Search Form -->
        <div class="search-wrapper mb-3">
            <form method="post" action="<?php echo routePath('knowledgebase-search');?>
">
                <div class="input-group">
                    <div class="input-group-prepend">
                        <button class="btn btn-outline-secondary" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <input class="form-control" type="text" name="search"
                        placeholder="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'searchOurKnowledgebase'),$_smarty_tpl ) );?>
...">
                </div>
            </form>
        </div>
        <!-- Primary Navbar for Sidebar -->
        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-navbar.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('navbar'=>$_smarty_tpl->tpl_vars['primaryNavbar']->value), 0, true);
?>
        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/login.php" class="btn btn-primary">
            <i class="fas fa-sign-in-alt"></i> Login
        </a>
    </div>
    <div class="sidebar-footer p-3 border-top">

    <?php if ($_smarty_tpl->tpl_vars['languagechangeenabled']->value && count($_smarty_tpl->tpl_vars['locales']->value) > 1 || $_smarty_tpl->tpl_vars['currencies']->value) {?>
        <button type="button" class="btn btn-outline-secondary" data-toggle="modal" data-target="#modalChooseLanguage">
            <div class="d-inline-block align-middle mr-2">
                <div class="iti-flag <?php if ($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'] === 'GB') {?>us<?php } else {
echo mb_strtolower($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'], 'UTF-8');
}?>"></div>
            </div>
            <?php echo $_smarty_tpl->tpl_vars['activeLocale']->value['localisedName'];?>
 / <?php echo $_smarty_tpl->tpl_vars['activeCurrency']->value['prefix'];?>
 <?php echo $_smarty_tpl->tpl_vars['activeCurrency']->value['code'];?>

        </button>
    <?php }?>

        <div class="social mt-3">
            <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/social-accounts.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
        </div>
    </div>
</div><?php }
}
