{*
 * Lahza.io Order Form Integration for WHMCS WIDDX Template
 * 
 * Enhanced integration for order forms with modern UI/UX
 * Supports all WHMCS order form templates
 * 
 * <AUTHOR> Development Team
 * @version 3.0.0
 *}

{* Enhanced Order Form Integration Styles *}
<style>
/* Order Form Specific Styles */
.order-form-lahza-integration {
    background: var(--bg-card, #ffffff);
    border-radius: var(--radius-lg, 0.75rem);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    margin: 1.5rem 0;
    overflow: hidden;
    transition: all var(--transition-normal, 250ms ease-in-out);
}

.order-form-lahza-integration:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
}

/* Payment Gateway Selection Enhancement */
.payment-gateway-selection {
    padding: 1.5rem;
    background: var(--gray-50, #f9fafb);
    border-bottom: 1px solid var(--border-light, #e5e7eb);
}

.payment-gateway-selection h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary, #111827);
    font-weight: 600;
    font-size: 1.25rem;
}

.payment-gateway-option {
    display: flex;
    align-items: center;
    padding: 1rem;
    margin: 0.5rem 0;
    background: white;
    border: 2px solid var(--border-light, #e5e7eb);
    border-radius: var(--radius-md, 0.5rem);
    cursor: pointer;
    transition: all var(--transition-normal, 250ms ease-in-out);
}

.payment-gateway-option:hover {
    border-color: var(--primary-color, #4a338d);
    background: var(--primary-50, #f3f4f6);
}

.payment-gateway-option.selected {
    border-color: var(--primary-color, #4a338d);
    background: var(--primary-50, #f3f4f6);
    box-shadow: 0 0 0 3px rgba(74, 51, 141, 0.1);
}

.payment-gateway-option input[type="radio"] {
    margin-right: 1rem;
    transform: scale(1.2);
}

.payment-gateway-logo {
    width: 40px;
    height: 40px;
    margin-right: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: var(--radius-md, 0.5rem);
    color: white;
    font-size: 1.25rem;
}

.payment-gateway-info {
    flex: 1;
}

.payment-gateway-name {
    font-weight: 600;
    color: var(--text-primary, #111827);
    margin-bottom: 0.25rem;
}

.payment-gateway-description {
    font-size: 0.875rem;
    color: var(--text-secondary, #6b7280);
}

.payment-gateway-features {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.payment-feature-badge {
    background: var(--success-100, #dcfce7);
    color: var(--success-700, #15803d);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm, 0.25rem);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Lahza Payment Form Integration */
.lahza-order-form-container {
    padding: 2rem;
    background: white;
}

.lahza-order-summary {
    background: var(--gray-50, #f9fafb);
    border-radius: var(--radius-md, 0.5rem);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-light, #e5e7eb);
}

.lahza-order-summary h5 {
    margin: 0 0 1rem 0;
    color: var(--text-primary, #111827);
    font-weight: 600;
}

.order-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light, #e5e7eb);
}

.order-summary-item:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--primary-color, #4a338d);
}

.order-summary-label {
    color: var(--text-secondary, #6b7280);
}

.order-summary-value {
    color: var(--text-primary, #111827);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .order-form-lahza-integration {
        margin: 1rem;
        border-radius: var(--radius-md, 0.5rem);
    }
    
    .payment-gateway-selection,
    .lahza-order-form-container {
        padding: 1rem;
    }
    
    .payment-gateway-option {
        padding: 0.75rem;
    }
    
    .payment-gateway-features {
        flex-wrap: wrap;
    }
}

/* Dark Mode Support */
[data-bs-theme="dark"] .order-form-lahza-integration {
    background: var(--gray-800, #1f2937);
    border: 1px solid var(--gray-700, #374151);
}

[data-bs-theme="dark"] .payment-gateway-selection {
    background: var(--gray-700, #374151);
    border-bottom-color: var(--gray-600, #4b5563);
}

[data-bs-theme="dark"] .payment-gateway-option {
    background: var(--gray-800, #1f2937);
    border-color: var(--gray-600, #4b5563);
}

[data-bs-theme="dark"] .payment-gateway-option:hover,
[data-bs-theme="dark"] .payment-gateway-option.selected {
    background: var(--gray-700, #374151);
}

[data-bs-theme="dark"] .lahza-order-summary {
    background: var(--gray-700, #374151);
    border-color: var(--gray-600, #4b5563);
}

[data-bs-theme="dark"] .order-summary-item {
    border-bottom-color: var(--gray-600, #4b5563);
}

/* RTL Support */
[dir="rtl"] .payment-gateway-option input[type="radio"] {
    margin-right: 0;
    margin-left: 1rem;
}

[dir="rtl"] .payment-gateway-logo {
    margin-right: 0;
    margin-left: 1rem;
}
</style>

{* Enhanced Payment Gateway Selection *}
<div class="order-form-lahza-integration">
    <div class="payment-gateway-selection">
        <h4>
            <i class="fas fa-credit-card me-2"></i>
            {if $language == 'arabic'}اختر طريقة الدفع{else}Choose Payment Method{/if}
        </h4>
        
        {* Lahza Payment Option *}
        <div class="payment-gateway-option selected" data-gateway="lahza">
            <input type="radio" name="payment_gateway" value="lahza" checked>
            <div class="payment-gateway-logo">
                <i class="fas fa-bolt"></i>
            </div>
            <div class="payment-gateway-info">
                <div class="payment-gateway-name">Lahza.io</div>
                <div class="payment-gateway-description">
                    {if $language == 'arabic'}
                        دفع سريع وآمن بجميع الطرق المتاحة
                    {else}
                        Fast and secure payment with all available methods
                    {/if}
                </div>
                <div class="payment-gateway-features">
                    <span class="payment-feature-badge">
                        <i class="fas fa-credit-card me-1"></i>
                        {if $language == 'arabic'}بطاقات{else}Cards{/if}
                    </span>
                    <span class="payment-feature-badge">
                        <i class="fas fa-university me-1"></i>
                        {if $language == 'arabic'}بنوك{else}Banks{/if}
                    </span>
                    <span class="payment-feature-badge">
                        <i class="fas fa-mobile-alt me-1"></i>
                        {if $language == 'arabic'}محافظ{else}Mobile{/if}
                    </span>
                    <span class="payment-feature-badge">
                        <i class="fas fa-qrcode me-1"></i>
                        QR
                    </span>
                </div>
            </div>
        </div>
        
        {* Other Payment Gateways (if any) *}
        {foreach $otherGateways as $gateway}
        <div class="payment-gateway-option" data-gateway="{$gateway.name}">
            <input type="radio" name="payment_gateway" value="{$gateway.name}">
            <div class="payment-gateway-logo">
                {if $gateway.logo}
                    <img src="{$gateway.logo}" alt="{$gateway.displayName}" style="max-width: 100%; max-height: 100%;">
                {else}
                    <i class="fas fa-credit-card"></i>
                {/if}
            </div>
            <div class="payment-gateway-info">
                <div class="payment-gateway-name">{$gateway.displayName}</div>
                <div class="payment-gateway-description">{$gateway.description}</div>
            </div>
        </div>
        {/foreach}
    </div>
    
    {* Lahza Payment Form Container *}
    <div class="lahza-order-form-container" id="lahza-payment-container">
        {* Order Summary *}
        <div class="lahza-order-summary">
            <h5>
                <i class="fas fa-receipt me-2"></i>
                {if $language == 'arabic'}ملخص الطلب{else}Order Summary{/if}
            </h5>
            
            {foreach $orderItems as $item}
            <div class="order-summary-item">
                <span class="order-summary-label">{$item.name}</span>
                <span class="order-summary-value">{$item.price}</span>
            </div>
            {/foreach}
            
            {if $discount}
            <div class="order-summary-item">
                <span class="order-summary-label">
                    {if $language == 'arabic'}خصم{else}Discount{/if}
                </span>
                <span class="order-summary-value text-success">-{$discount}</span>
            </div>
            {/if}
            
            {if $tax}
            <div class="order-summary-item">
                <span class="order-summary-label">
                    {if $language == 'arabic'}ضريبة{else}Tax{/if}
                </span>
                <span class="order-summary-value">{$tax}</span>
            </div>
            {/if}
            
            <div class="order-summary-item">
                <span class="order-summary-label">
                    {if $language == 'arabic'}المجموع{else}Total{/if}
                </span>
                <span class="order-summary-value">{$total}</span>
            </div>
        </div>
        
        {* Include Lahza Payment Form *}
        {include file="payment/lahza/payment-form.tpl"}
    </div>
</div>

{* Enhanced JavaScript for Order Form Integration *}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment gateway selection handling
    const gatewayOptions = document.querySelectorAll('.payment-gateway-option');
    const lahzaContainer = document.getElementById('lahza-payment-container');
    
    gatewayOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            gatewayOptions.forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Check the radio button
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
            
            // Show/hide Lahza container based on selection
            const gateway = this.dataset.gateway;
            if (gateway === 'lahza') {
                lahzaContainer.style.display = 'block';
                // Reinitialize Lahza payment handler if needed
                if (window.lahzaPaymentHandler) {
                    window.lahzaPaymentHandler.setupElements();
                }
            } else {
                lahzaContainer.style.display = 'none';
            }
        });
    });
    
    // Enhanced order form validation
    const orderForm = document.querySelector('form[name="orderfrm"]');
    if (orderForm) {
        orderForm.addEventListener('submit', function(e) {
            const selectedGateway = document.querySelector('input[name="payment_gateway"]:checked');
            
            if (selectedGateway && selectedGateway.value === 'lahza') {
                // Prevent default form submission for Lahza payments
                e.preventDefault();
                
                // Trigger Lahza payment instead
                if (window.lahzaPaymentHandler) {
                    window.lahzaPaymentHandler.triggerPayment();
                } else {
                    console.error('Lahza payment handler not initialized');
                }
            }
        });
    }
    
    // Order summary animation
    const summaryItems = document.querySelectorAll('.order-summary-item');
    summaryItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.3s ease-out';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Enhanced error handling for order forms
    window.addEventListener('lahza-payment-error', function(e) {
        const errorMessage = e.detail.message;
        
        // Show error in order form context
        const errorContainer = document.createElement('div');
        errorContainer.className = 'alert alert-danger mt-3';
        errorContainer.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${errorMessage}
        `;
        
        const container = document.querySelector('.lahza-order-form-container');
        if (container) {
            container.insertBefore(errorContainer, container.firstChild);
            
            // Auto-remove error after 5 seconds
            setTimeout(() => {
                errorContainer.remove();
            }, 5000);
        }
    });
    
    // Success handling
    window.addEventListener('lahza-payment-success', function(e) {
        const transaction = e.detail.transaction;
        
        // Show success message
        const successContainer = document.createElement('div');
        successContainer.className = 'alert alert-success mt-3';
        successContainer.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            {if $language == 'arabic'}
                تم الدفع بنجاح! جاري التحويل...
            {else}
                Payment successful! Redirecting...
            {/if}
        `;
        
        const container = document.querySelector('.lahza-order-form-container');
        if (container) {
            container.insertBefore(successContainer, container.firstChild);
        }
        
        // Redirect after success
        setTimeout(() => {
            window.location.href = e.detail.redirectUrl || '/clientarea.php';
        }, 2000);
    });
});

// Utility function for order form integration
window.LahzaOrderFormUtils = {
    updateOrderSummary: function(newTotal) {
        const totalElement = document.querySelector('.order-summary-item:last-child .order-summary-value');
        if (totalElement) {
            totalElement.textContent = newTotal;
        }
    },
    
    showProcessingState: function() {
        const container = document.querySelector('.lahza-order-form-container');
        if (container) {
            container.style.opacity = '0.7';
            container.style.pointerEvents = 'none';
        }
    },
    
    hideProcessingState: function() {
        const container = document.querySelector('.lahza-order-form-container');
        if (container) {
            container.style.opacity = '1';
            container.style.pointerEvents = 'auto';
        }
    }
};
</script>

{* Order Form Specific CSS Enhancements *}
<style>
/* Animation for order form appearance */
.order-form-lahza-integration {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced hover effects for better UX */
.payment-gateway-option {
    position: relative;
    overflow: hidden;
}

.payment-gateway-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(74, 51, 141, 0.1), transparent);
    transition: left 0.5s;
}

.payment-gateway-option:hover::before {
    left: 100%;
}

/* Loading state for order processing */
.order-processing {
    position: relative;
}

.order-processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.order-processing::after {
    content: '⏳ Processing...';
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--primary-color, #4a338d);
}

/* Success state animation */
.order-success {
    animation: successPulse 0.8s ease-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}
</style>