# Lahza.io Payment Gateway - Troubleshooting Guide

## Quick Diagnostics

### 🔍 Common Issues Checklist

Before diving into detailed troubleshooting, check these common issues:

- [ ] SSL/HTTPS is enabled on your website
- [ ] Lahza.io gateway is enabled in WHMCS admin
- [ ] API keys are correctly configured (test vs live)
- [ ] Webhook URL is accessible from the internet
- [ ] JavaScript is enabled in customer's browser
- [ ] No browser ad-blockers blocking payment scripts

## Issue Categories

### 1. Payment Button Not Working

#### Symptoms:
- Payment button doesn't respond to clicks
- No popup or redirect occurs
- JavaScript errors in browser console

#### Diagnosis:
```javascript
// Open browser console (F12) and check for errors
console.log('Lahza payment data:', window.lahzaPaymentData);
console.log('Lahza handler:', window.lahzaPaymentHandler);
```

#### Solutions:

**A. JavaScript Library Not Loaded**
```html
<!-- Check if this script is loaded -->
<script src="https://js.lahza.io/inline.min.js"></script>
```

**B. Payment Data Missing**
```javascript
// Verify payment data is available
if (!window.lahzaPaymentData) {
    console.error('Lahza payment data not initialized');
}
```

**C. Handler Not Initialized**
```javascript
// Manually initialize if needed
if (!window.lahzaPaymentHandler) {
    window.lahzaPaymentHandler = new LahzaPaymentHandler({
        enableLogging: true
    });
}
```

### 2. Webhook Not Received

#### Symptoms:
- Payment successful in Lahza.io but not marked as paid in WHMCS
- No transaction record in WHMCS logs
- Invoice remains unpaid

#### Diagnosis:
```bash
# Test webhook URL accessibility
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

#### Solutions:

**A. Webhook URL Not Accessible**
- Ensure URL is publicly accessible
- Check firewall settings
- Verify SSL certificate is valid

**B. IP Whitelist Issues**
```php
// In lahza.php gateway configuration
'ipWhitelist' => '*************,**************'
```

**C. Webhook Signature Verification**
```php
// Check webhook signature in callback
function lahza_verifyWebhookSignature($payload, $signature, $secretKey) {
    $signature = str_replace('sha256=', '', $signature);
    $expectedSignature = hash_hmac('sha256', $payload, $secretKey);
    return hash_equals($expectedSignature, $signature);
}
```

### 3. Currency Not Supported

#### Symptoms:
- Error message about unsupported currency
- Payment form doesn't load

#### Diagnosis:
```php
// Check supported currencies
$supportedCurrencies = ['ILS', 'USD', 'JOD'];
$currentCurrency = $params['currency'];
if (!in_array($currentCurrency, $supportedCurrencies)) {
    // Currency not supported
}
```

#### Solutions:

**A. Add Currency Support**
1. Go to WHMCS Admin → Setup → Payments → Currencies
2. Ensure ILS, USD, or JOD is configured
3. Set appropriate exchange rates

**B. Force Specific Currency**
```php
// In gateway configuration
if (!in_array($currency, ['ILS', 'USD', 'JOD'])) {
    $currency = 'USD'; // Default fallback
}
```

### 4. Template Integration Issues

#### Symptoms:
- Payment form doesn't appear
- Styling is broken
- Template conflicts

#### Diagnosis:
```php
// Check if template files exist
$templatePath = ROOTDIR . '/templates/widdx/payment/lahza/';
$requiredFiles = [
    'payment-form.tpl',
    'lahza-payment.js',
    'lahza-payment.css'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($templatePath . $file)) {
        echo "Missing: {$file}\n";
    }
}
```

#### Solutions:

**A. Template Files Missing**
- Ensure all template files are uploaded
- Check file permissions (644 for files, 755 for directories)

**B. Template Cache Issues**
```php
// Clear template cache
// WHMCS Admin → Utilities → System → Template Cache
```

**C. CSS Conflicts**
```css
/* Add specificity to Lahza styles */
.lahza-payment-container {
    /* Your styles with !important if needed */
}
```

### 5. API Connection Issues

#### Symptoms:
- "Failed to initialize payment" errors
- Timeout errors
- Connection refused errors

#### Diagnosis:
```php
// Test API connectivity
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://api.lahza.io/');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: {$httpCode}\n";
echo "Error: {$error}\n";
```

#### Solutions:

**A. Firewall Blocking Outbound Connections**
- Whitelist Lahza.io IP addresses
- Allow HTTPS (port 443) outbound connections

**B. cURL Configuration**
```php
// Enhanced cURL settings
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_USERAGENT, 'WHMCS-Lahza/3.0');
```

**C. Proxy Configuration**
```php
// If behind proxy
curl_setopt($ch, CURLOPT_PROXY, 'proxy.example.com:8080');
curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'username:password');
```

## Debug Mode

### Enable Comprehensive Logging

**1. Gateway Configuration**
```php
// In WHMCS Admin → Payment Gateways → Lahza.io
'enableLogging' => true
```

**2. JavaScript Debug Mode**
```javascript
// Add to payment page
window.lahzaEnableLogging = true;
```

**3. PHP Error Logging**
```php
// Add to callback file
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```

### Log Locations

**WHMCS Logs:**
- Module Log: `Setup → Logs → Module Log`
- Gateway Log: `Setup → Logs → Gateway Log`
- Activity Log: `Setup → Logs → Activity Log`

**Server Logs:**
- Apache: `/var/log/apache2/error.log`
- Nginx: `/var/log/nginx/error.log`
- PHP: `/var/log/php_errors.log`

## Testing Procedures

### 1. Test Mode Setup

```php
// Gateway configuration for testing
$config = [
    'testMode' => true,
    'publicKey' => 'pk_test_your_test_key',
    'secretKey' => 'sk_test_your_test_key',
    'enableLogging' => true
];
```

### 2. Test Payment Flow

**A. Frontend Test**
1. Add item to cart
2. Proceed to checkout
3. Select Lahza.io payment
4. Click payment button
5. Complete test payment

**B. Backend Verification**
1. Check WHMCS logs for payment initiation
2. Verify webhook received
3. Confirm invoice marked as paid
4. Check transaction details

### 3. Test Cards

```
Visa Success: ****************
Visa Decline: ****************
Mastercard Success: ****************
Mastercard Decline: 5000000000000009
```

## Performance Optimization

### 1. Frontend Optimization

**A. Lazy Load Payment Scripts**
```javascript
// Load Lahza script only when needed
function loadLahzaScript() {
    if (!document.querySelector('script[src*="lahza.io"]')) {
        const script = document.createElement('script');
        script.src = 'https://js.lahza.io/inline.min.js';
        script.async = true;
        document.head.appendChild(script);
    }
}
```

**B. Cache Static Assets**
```apache
# .htaccess for caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>
```

### 2. Backend Optimization

**A. Connection Pooling**
```php
// Reuse cURL handles
class LahzaAPIClient {
    private static $curlHandle = null;
    
    public static function getCurlHandle() {
        if (self::$curlHandle === null) {
            self::$curlHandle = curl_init();
            // Set common options
        }
        return self::$curlHandle;
    }
}
```

**B. Response Caching**
```php
// Cache successful verifications temporarily
$cacheKey = "lahza_verify_{$reference}";
$cached = cache_get($cacheKey);
if ($cached) {
    return $cached;
}
```

## Security Hardening

### 1. Webhook Security

**A. Signature Verification**
```php
// Always verify webhook signatures
if (!lahza_verifyWebhookSignature($payload, $signature, $secretKey)) {
    http_response_code(401);
    die('Invalid signature');
}
```

**B. IP Whitelisting**
```php
// Restrict webhook sources
$allowedIPs = ['*************', '**************'];
$clientIP = $_SERVER['REMOTE_ADDR'];
if (!in_array($clientIP, $allowedIPs)) {
    http_response_code(403);
    die('IP not allowed');
}
```

### 2. API Key Security

**A. Environment Variables**
```php
// Store keys in environment variables
$publicKey = getenv('LAHZA_PUBLIC_KEY');
$secretKey = getenv('LAHZA_SECRET_KEY');
```

**B. Key Rotation**
```php
// Implement key rotation
function rotateLahzaKeys() {
    // Generate new keys
    // Update configuration
    // Notify administrators
}
```

## Common Error Messages

### "Payment gateway not configured properly"
- Check API keys are set
- Verify test/live mode settings
- Ensure gateway is enabled

### "Invalid payment amount"
- Check amount is greater than 0
- Verify currency conversion
- Ensure proper number formatting

### "Transaction verification failed"
- Check webhook signature
- Verify API connectivity
- Review transaction reference format

### "Unsupported currency"
- Use ILS, USD, or JOD only
- Check WHMCS currency configuration
- Verify exchange rates

## Support Resources

### Documentation
- [Lahza.io API Docs](https://docs.lahza.io/)
- [WHMCS Payment Gateway Docs](https://docs.whmcs.com/Payment_Gateways)
- [WIDDX Template Docs](template-documentation-link)

### Contact Support
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/
- **Emergency Issues**: Check status pages first

### Community Resources
- WHMCS Community Forums
- Lahza.io Developer Community
- Stack Overflow (tag: whmcs, lahza)

## Maintenance Checklist

### Daily
- [ ] Monitor payment success rates
- [ ] Check error logs for issues
- [ ] Verify webhook delivery

### Weekly
- [ ] Review transaction logs
- [ ] Test payment flow
- [ ] Check API response times

### Monthly
- [ ] Update gateway module if available
- [ ] Review security settings
- [ ] Rotate API keys (if policy requires)
- [ ] Performance optimization review

### Quarterly
- [ ] Full integration testing
- [ ] Security audit
- [ ] Documentation updates
- [ ] Backup configuration

---

**Last Updated**: January 2025  
**Version**: 3.0.0  
**For Technical Support**: Create detailed issue reports with logs and error messages