<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:17:11
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\sections\widdx-productgroups.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0e27c25505_32201351',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '422246a77ae33ae3a77b25b64ba2a84ae92f780b' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\sections\\widdx-productgroups.tpl',
      1 => 1749509403,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0e27c25505_32201351 (Smarty_Internal_Template $_smarty_tpl) {
if (!empty($_smarty_tpl->tpl_vars['productGroups']->value) || $_smarty_tpl->tpl_vars['registerdomainenabled']->value || $_smarty_tpl->tpl_vars['transferdomainenabled']->value) {?>
<section class="productgroups-section transparent-section">
    <div class="container">

        <h2 class="text-center m-4 ptb-100"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'clientHomePanels.productsAndServices'),$_smarty_tpl ) );?>
</h2>

        <!-- سلايدر Owl Carousel -->
        <div class="owl-carousel owl-theme">
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['productGroups']->value, 'productGroup');
$_smarty_tpl->tpl_vars['productGroup']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['productGroup']->value) {
$_smarty_tpl->tpl_vars['productGroup']->do_else = false;
?>
                <div class="item">
                    <div class="card h-100 mb-3">
                        <div class="widdxcard-body d-flex flex-column text-center">
                            <?php if (!empty($_smarty_tpl->tpl_vars['productGroup']->value->name)) {?>
                                <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/frontend/assets/img/product-groups/<?php echo mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8');?>
.png"
                                    onerror="this.onerror=null;this.src='<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/frontend/assets/img/product-groups/<?php echo mb_strtolower($_smarty_tpl->tpl_vars['productGroup']->value->name, 'UTF-8');?>
.svg';"
                                    alt="<?php echo (($tmp = @$_smarty_tpl->tpl_vars['productGroup']->value->name)===null||$tmp==='' ? 'Unknown Product' : $tmp);?>
" class="img-fluid mx-auto mb-3"
                                    style="max-width: 200px; max-height: 200px;">
                            <?php } else { ?>
                                <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/widdx/frontend/assets/img/product-groups/default.png"
                                    alt="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'unknownProduct'),$_smarty_tpl ) );?>
" class="img-fluid mx-auto mb-3"
                                    style="max-width: 200px; max-height: 200px;">
                            <?php }?>
                            <h3 class="card-title mt-auto">
                                <?php echo (($tmp = @$_smarty_tpl->tpl_vars['productGroup']->value->name)===null||$tmp==='' ? 'Unknown Product' : $tmp);?>

                            </h3>
                            <p><?php echo $_smarty_tpl->tpl_vars['productGroup']->value->tagline;?>
</p>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['productGroup']->value->getRoutePath();?>
" class="btn btn-outline-primary btn-block mt-auto">
                                <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'browseProducts'),$_smarty_tpl ) );?>

                            </a>
                        </div>
                    </div>
                </div>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </div>
    <?php }?>
    </div>
</section>
<?php }
}
