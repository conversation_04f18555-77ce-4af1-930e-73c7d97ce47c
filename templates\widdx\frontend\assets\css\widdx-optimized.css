/**
 * WIDDX Optimized CSS - Reduced !important usage
 * This file replaces excessive !important declarations with proper CSS specificity
 */

/* Root Variables - No !important needed */
:root {
  --primary: var(--primary-color);
  --primary-dark: var(--primary-dark);
  --primary-light: var(--primary-light);
  --body-bg: var(--white);
  --text-color: var(--gray-700);
  --text-muted: var(--gray-500);
  --heading-color: var(--gray-900);
  --border-color: var(--gray-200);
  --success: var(--success-color);
  --warning: var(--warning-color);
  --error: var(--error-color);
  --info: var(--info-color);
}

/* Header Styles - Using proper specificity instead of !important */
.header {
  background-color: transparent;
}

.header .navbar-brand {
  color: var(--text-color);
  font-size: 1.5rem;
}

.header .nav-link {
  color: var(--text-color);
  padding: 10px;
}

.header .nav-link:hover,
.header .nav-link:focus {
  color: var(--primary);
  text-decoration: none;
}

/* Sidebar Styles */
.widdxsidebar {
  background: var(--btn-bg);
  box-shadow: 2px 0 5px rgba(var(--dark-rgb), 0.3);
  padding: 15px;
}

.widdxsidebar .sidebar-header {
  background-color: var(--btn-bg);
  color: var(--heading-color);
  border-bottom: 1px solid var(--border-color);
}

.widdxsidebar .nav-link {
  padding: 10px 15px;
  color: var(--body-color);
  text-decoration: none;
}

.widdxsidebar .nav-link:hover,
.widdxsidebar .nav-link:focus {
  background-color: var(--gray-light);
  color: var(--primary);
}

/* Button Styles */
.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover,
.btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* Utility Classes - Reduced !important usage */
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

/* Dark Theme - Using attribute selectors for better specificity */
[data-bs-theme="dark"] {
  --body-bg: var(--gray-900);
  --text-color: var(--gray-50);
  --heading-color: var(--gray-50);
  --border-color: var(--gray-600);
  --card-bg: var(--gray-800);
}

[data-bs-theme="dark"] .header {
  background-color: var(--gray-800);
  color: var(--text-color);
}

[data-bs-theme="dark"] .btn-primary {
  background-color: var(--primary);
  border-color: var(--primary-dark);
}

[data-bs-theme="dark"] .btn-primary:hover,
[data-bs-theme="dark"] .btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-darker);
}

/* RTL Support - Using logical properties where possible */
html[dir="rtl"] .text-left {
  text-align: right;
}

html[dir="rtl"] .text-right {
  text-align: left;
}

html[dir="rtl"] .float-left {
  float: right;
}

html[dir="rtl"] .float-right {
  float: left;
}

/* Form Elements */
.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--custom-radius);
}

.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Tables */
.table {
  color: var(--text-color);
  border-color: var(--border-color);
}

.table thead th {
  background-color: var(--gray-light);
  color: var(--heading-color);
  border-color: var(--border-color);
}

/* Dropdowns */
.dropdown-menu {
  background-color: var(--dropdown-bg);
  border: 1px solid var(--border-color);
  border-radius: var(--custom-radius);
}

.dropdown-item {
  color: var(--body-color);
}

.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--gray-light);
  color: var(--primary);
}

/* Alerts */
.alert {
  border-color: var(--border-color);
}

.alert-success {
  background-color: var(--success-light);
  color: var(--success);
  border-color: var(--success);
}

.alert-warning {
  background-color: var(--warning-light);
  color: var(--warning);
  border-color: var(--warning);
}

.alert-danger {
  background-color: var(--error-light);
  color: var(--error);
  border-color: var(--error);
}

.alert-info {
  background-color: var(--info-light);
  color: var(--info);
  border-color: var(--info);
}

/* Modals */
.modal-content {
  background-color: var(--modal-bg);
  border-color: var(--border-color);
}

.modal-header {
  border-bottom-color: var(--border-color);
}

.modal-footer {
  border-top-color: var(--border-color);
}

/* Pagination */
.pagination .page-link {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

.pagination .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

/* List Groups */
.list-group-item {
  background-color: var(--card-bg);
  border-color: var(--border-color);
  color: var(--text-color);
}

.list-group-item.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
  background-color: var(--gray-light);
}

/* Badges */
.badge-primary { background-color: var(--primary); }
.badge-secondary { background-color: var(--gray); }
.badge-success { background-color: var(--success); }
.badge-info { background-color: var(--info); }
.badge-warning { background-color: var(--warning); }
.badge-danger { background-color: var(--error); }

/* Transitions - Only use !important for critical animations */
.transition-fast { 
  transition: all var(--transition-fast);
}

.transition-normal { 
  transition: all var(--transition-normal);
}

.transition-slow { 
  transition: all var(--transition-slow);
}

/* Accessibility - Only use !important for critical accessibility features */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    border: 2px solid currentColor !important;
  }
  
  .form-control:focus {
    outline: 2px solid var(--primary) !important;
    outline-offset: 2px !important;
  }
}

/* Print styles - Use !important only for print-specific overrides */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}