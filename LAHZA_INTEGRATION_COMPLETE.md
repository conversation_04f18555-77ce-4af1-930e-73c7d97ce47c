# Lahza.io Payment Gateway - Complete Integration Guide for WHMCS & WIDDX Template

## Overview
This document provides a complete integration guide for the Lahza.io payment gateway with WHMCS and the WIDDX template. The integration includes both backend payment processing and frontend UI components optimized for the WIDDX template.

## Current Integration Status ✅

### ✅ Backend Integration (Complete)
- **Payment Gateway Module**: `modules/gateways/lahza.php` - Fully implemented
- **Callback Handler**: `modules/gateways/callback/lahza.php` - Complete with webhook support
- **API Integration**: Full Lahza.io API v2024 support
- **Security Features**: Webhook signature verification, IP whitelisting
- **Multi-currency Support**: ILS, USD, JOD
- **Transaction Logging**: Comprehensive logging with card type detection

### ✅ Frontend Integration (Complete)
- **WIDDX Template Integration**: `templates/widdx/payment/lahza/` - Complete
- **Payment Form**: Modern, responsive UI with RTL support
- **JavaScript Handler**: Advanced error handling and retry mechanisms
- **CSS Styling**: Optimized for WIDDX template with dark mode support
- **Order Form Integration**: Works with all WHMCS order forms

## File Structure

```
WHMCS Root/
├── modules/gateways/
│   ├── lahza.php                    # Main gateway module
│   ├── lahza_config.php            # Configuration helper
│   └── callback/
│       └── lahza.php               # Webhook handler
├── templates/widdx/payment/lahza/
│   ├── payment-form.tpl            # Payment form template
│   ├── lahza-payment.js            # JavaScript handler
│   ├── lahza-payment.css           # Styling
│   ├── config.php                  # Template configuration
│   ├── README.md                   # Documentation
│   └── TROUBLESHOOTING.md          # Troubleshooting guide
└── Documentation/
    ├── LAHZA_INTEGRATION_COMPLETE.md  # This file
    ├── CHANGELOG_LAHZA.md             # Version history
    ├── INSTALL_LAHZA.md               # Installation guide
    └── README_LAHZA.md                # Quick reference
```

## Configuration Guide

### 1. Gateway Configuration (Admin Area)

Navigate to **Setup > Payments > Payment Gateways** and configure:

#### Required Settings:
- **Public Key**: Your Lahza.io public key (pk_test_... or pk_live_...)
- **Secret Key**: Your Lahza.io secret key (sk_test_... or sk_live_...)
- **Test Mode**: Enable for testing, disable for production

#### Optional Settings:
- **Payment Method**: Popup (recommended) or Redirect
- **Payment Channels**: card,bank,mobile_money,qr,ussd,bank_transfer
- **Webhook URL**: Auto-generated or custom
- **Callback URL**: Auto-generated or custom
- **Enable Logging**: For debugging and monitoring
- **Show Card Type**: Display card information in transaction logs
- **Custom CSS**: Additional styling
- **IP Whitelist**: Lahza.io webhook IPs (*************,**************)

### 2. Webhook Configuration

#### Automatic Setup:
The webhook URL is automatically generated as:
```
https://yourdomain.com/modules/gateways/callback/lahza.php
```

#### Manual Setup (if needed):
1. Login to your Lahza.io dashboard
2. Navigate to Settings > Webhooks
3. Add webhook URL: `https://yourdomain.com/modules/gateways/callback/lahza.php`
4. Select events: `charge.success`, `refund.processed`

### 3. Template Integration

The WIDDX template integration is already complete. The payment form will automatically appear when:
- Lahza.io gateway is enabled
- Customer proceeds to checkout
- Invoice payment is processed

## Features

### 🚀 Advanced Features

#### Payment Methods Support:
- **Card Payments**: Visa, Mastercard, American Express
- **Bank Transfers**: Direct bank integration
- **Mobile Money**: Mobile wallet payments
- **QR Codes**: Quick payment via QR scanning
- **USSD**: USSD-based payments

#### Security Features:
- **SSL Encryption**: 256-bit SSL encryption
- **Webhook Verification**: HMAC signature verification
- **IP Whitelisting**: Restrict webhook sources
- **Transaction Validation**: Comprehensive validation
- **Fraud Protection**: Built-in fraud detection

#### User Experience:
- **Responsive Design**: Mobile-optimized interface
- **RTL Support**: Arabic, Hebrew, Farsi languages
- **Dark Mode**: Automatic theme detection
- **Loading States**: Visual feedback during processing
- **Error Handling**: User-friendly error messages
- **Retry Mechanism**: Automatic retry for failed payments

#### Developer Features:
- **Comprehensive Logging**: Detailed transaction logs
- **Card Type Detection**: Display card brand and bank
- **Metadata Support**: Custom transaction data
- **API Integration**: Full Lahza.io API support
- **Webhook Processing**: Real-time payment updates

### 🎨 UI/UX Features

#### Modern Design:
- **Gradient Backgrounds**: Beautiful visual design
- **Smooth Animations**: Enhanced user experience
- **Card-based Layout**: Clean, modern interface
- **Icon Integration**: FontAwesome icons
- **Responsive Grid**: Bootstrap-based layout

#### Accessibility:
- **Screen Reader Support**: ARIA labels and roles
- **Keyboard Navigation**: Full keyboard accessibility
- **High Contrast**: Accessible color schemes
- **Font Scaling**: Responsive typography
- **Focus Indicators**: Clear focus states

## API Integration Details

### Supported Endpoints:
- `POST /transaction/initialize` - Initialize payment
- `GET /transaction/verify/{reference}` - Verify payment
- `POST /transaction/refund` - Process refunds

### Webhook Events:
- `charge.success` - Payment completed successfully
- `charge.failed` - Payment failed
- `refund.processed` - Refund completed

### Metadata Structure:
```json
{
  "invoice_id": "12345",
  "client_id": "67890",
  "company_name": "Your Company",
  "whmcs_url": "https://yourdomain.com",
  "payment_method": "lahza",
  "client_name": "John Doe",
  "custom_fields": [
    {
      "display_name": "Invoice ID",
      "variable_name": "invoice_id",
      "value": "12345"
    }
  ]
}
```

## Troubleshooting

### Common Issues:

#### 1. Payment Not Processing
**Symptoms**: Payment button doesn't work
**Solutions**:
- Check if Lahza.io JavaScript library is loaded
- Verify public key format
- Check browser console for errors
- Ensure SSL is enabled

#### 2. Webhook Not Received
**Symptoms**: Payments successful but not marked as paid in WHMCS
**Solutions**:
- Verify webhook URL is accessible
- Check IP whitelist settings
- Verify webhook signature
- Check server logs

#### 3. Currency Not Supported
**Symptoms**: Error about unsupported currency
**Solutions**:
- Ensure currency is ILS, USD, or JOD
- Check WHMCS currency configuration
- Verify Lahza.io account supports currency

#### 4. Template Not Loading
**Symptoms**: Payment form doesn't appear
**Solutions**:
- Verify template files exist
- Check file permissions
- Clear template cache
- Verify gateway is enabled

### Debug Mode:
Enable logging in gateway configuration to see detailed debug information in:
- **WHMCS Activity Log**: Setup > Logs > Module Log
- **Gateway Log**: Setup > Logs > Gateway Log
- **Browser Console**: F12 Developer Tools

## Testing

### Test Mode Configuration:
1. Enable "Test Mode" in gateway settings
2. Use test API keys (pk_test_... and sk_test_...)
3. Use test card numbers provided by Lahza.io

### Test Cards:
```
Visa: ****************
Mastercard: ****************
American Express: ***************
```

### Test Scenarios:
- ✅ Successful payment
- ❌ Failed payment (insufficient funds)
- 🔄 Cancelled payment
- 📱 Mobile payment
- 🏦 Bank transfer
- ���� Different card types

## Performance Optimization

### Frontend Optimizations:
- **Minified Assets**: Compressed CSS and JS
- **Lazy Loading**: Load payment form on demand
- **Caching**: Browser caching for static assets
- **CDN Integration**: Use CDN for Lahza.io library

### Backend Optimizations:
- **Connection Pooling**: Reuse HTTP connections
- **Timeout Handling**: Proper timeout configuration
- **Error Caching**: Cache failed requests temporarily
- **Database Indexing**: Optimize transaction queries

## Security Best Practices

### Server Security:
- **SSL Certificate**: Always use HTTPS
- **Firewall Rules**: Restrict webhook access
- **Regular Updates**: Keep WHMCS updated
- **Access Control**: Limit admin access

### API Security:
- **Key Rotation**: Regularly rotate API keys
- **Environment Variables**: Store keys securely
- **Webhook Verification**: Always verify signatures
- **Rate Limiting**: Implement rate limiting

## Maintenance

### Regular Tasks:
- **Monitor Logs**: Check for errors regularly
- **Update Keys**: Rotate API keys periodically
- **Test Payments**: Regular payment testing
- **Backup Configuration**: Backup gateway settings

### Updates:
- **Gateway Module**: Check for updates monthly
- **Lahza.io API**: Monitor API changes
- **WHMCS Compatibility**: Test with WHMCS updates
- **Template Updates**: Update WIDDX template integration

## Support

### Documentation:
- **Lahza.io Docs**: https://docs.lahza.io/
- **WHMCS Docs**: https://docs.whmcs.com/
- **WIDDX Template**: Template documentation

### Contact Information:
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/
- **WIDDX Support**: Template support channels

## Version History

### v3.0.0 (Current)
- ✅ Complete WHMCS 8.x compatibility
- ✅ Full WIDDX template integration
- ✅ Enhanced security features
- ✅ Improved error handling
- ✅ RTL language support
- ✅ Dark mode support
- ✅ Mobile optimization

### v2.0.0
- ✅ Webhook signature verification
- ✅ Card type detection
- ✅ Multi-currency support
- ✅ Enhanced logging

### v1.0.0
- ✅ Basic payment processing
- ✅ API integration
- ✅ Simple UI

## Conclusion

The Lahza.io payment gateway integration with WHMCS and the WIDDX template is now complete and production-ready. The integration provides:

- **Seamless Payment Experience**: Modern, user-friendly interface
- **Comprehensive Security**: Industry-standard security measures
- **Full Feature Support**: All Lahza.io payment methods
- **Developer-Friendly**: Extensive logging and debugging
- **Multi-language Support**: RTL and LTR languages
- **Mobile Optimization**: Responsive design for all devices

The integration is designed to be maintainable, scalable, and future-proof, ensuring long-term compatibility with both WHMCS and Lahza.io platform updates.

---

**Last Updated**: January 2025  
**Version**: 3.0.0  
**Compatibility**: WHMCS 8.x, WIDDX Template, Lahza.io API v2024