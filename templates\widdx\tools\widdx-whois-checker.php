<?php
// Include enhanced session manager
require_once __DIR__ . '/../helpers/WiddxSessionManager.php';
require_once __DIR__ . '/../helpers/WiddxErrorHandler.php';

function handleWhoisCheck()
{
    $sessionManager = WiddxSessionManager::getInstance();
    
    // Clear any previous WHOIS results
    $sessionManager->remove('whoisResult');

    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['domain'])) {
        // Use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
        $domain = filter_var(trim($_POST['domain']), FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        
        // Additional domain validation and sanitization
        $domain = preg_replace('/[^a-zA-Z0-9\-\.]/', '', $domain);
        
        // Remove http:// or https://
        $domain = preg_replace('#^https?://#', '', $domain);
        
        // Remove www. if present
        $domain = preg_replace('/^www\./', '', $domain);
        
        // Remove trailing slash
        $domain = rtrim($domain, '/');

        $whoisResult = '';
        $responseTime = '';
        $error = '';

        if (!empty($domain) && preg_match('/^[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$/', $domain)) {
            $tld = getTldFromDomain($domain);
            $whoisServer = getWhoisServer($tld);

            if ($whoisServer) {
                // استخدام API أولاً
                $startTime = microtime(true);
                $apiResult = getWhoisInfo($domain);

                if ($apiResult && isset($apiResult['whois_record'])) {
                    $whoisResult = $apiResult['whois_record'];
                    $responseTime = microtime(true) - $startTime;
                } else {
                    // استخدام الاتصال المباشر بالخادم في حالة فشل API
                    $whoisResult = queryWhoisServer($whoisServer, $domain);
                    $responseTime = microtime(true) - $startTime;

                    if ($whoisResult === false) {
                        $error = "Unable to retrieve WHOIS information. Please try again later.";
                    }
                }
            } else {
                $error = "WHOIS server not found for this TLD ($tld). Please check the domain extension.";
            }
        } else {
            $error = "Invalid domain name format. Please enter a valid domain.";
        }

        if (!$error) {
            $sessionManager->set('whoisResult', $whoisResult);
            $parsedResult = parseWhoisResult($whoisResult);
            $sslInfo = checkSSL($domain);
            return formatWhoisResult($parsedResult, $responseTime, $domain, $sslInfo);
        } else {
            return "<div class='alert alert-danger mt-4'>{$error}</div>";
        }
    }
    return null;
}

function getTldFromDomain($domain)
{
    $parts = explode('.', $domain);
    if (count($parts) > 2) {
        $tld = implode('.', array_slice($parts, -2));
    } else {
        $tld = end($parts);
    }
    return strtolower($tld);
}

function getWhoisInfo($domain)
{
    $apiKey = '9t6JZ27pp1_Rg6VLw3C-Wg'; // وضع مفتاح API الخاص بك هنا
    $url = "https://jsonwhoisapi.com/api/v1/whois?identifier={$domain}";

    $headers = [
        "Authorization: Token token={$apiKey}"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

function getWhoisServer($tld)
{
    $whoisServers = [
        'com' => 'whois.verisign-grs.com',
        'net' => 'whois.verisign-grs.com',
        'org' => 'whois.pir.org',
        'info' => 'whois.afilias.net',
        'biz' => 'whois.biz',
        'io' => 'whois.nic.io',
        'co' => 'whois.nic.co',
        'me' => 'whois.nic.me',
        'us' => 'whois.nic.us',
        'ca' => 'whois.cira.ca',
        'tv' => 'whois.nic.tv',
        'name' => 'whois.nic.name',
        'pro' => 'whois.registrypro.pro',
        'mobi' => 'whois.dotmobi.mobi',
        'cc' => 'whois.nic.cc',
        'asia' => 'whois.nic.asia',
        'tel' => 'whois.nic.tel',
        'eu' => 'whois.eu',
        'co.uk' => 'whois.nic.uk',
        'uk' => 'whois.nic.uk',
        'ws' => 'whois.nic.ws',
        'de' => 'whois.denic.de',
        'fr' => 'whois.nic.fr',
        'au' => 'whois.audn.au',
        'jp' => 'whois.jprs.jp',
        'cn' => 'whois.cnnic.cn',
        'in' => 'whois.inregistry.in',
        'ru' => 'whois.ripn.net',
        'br' => 'whois.registro.br',
        'mx' => 'whois.nic.mx',
        'be' => 'whois.dns.be',
        'ch' => 'whois.nic.ch',
        'at' => 'whois.nic.at',
        'ps' => 'whois.pnina.ps',
        'co.il' => 'whois.isoc.org.il',
        'online' => 'whois.nic.online',
        'ai' => 'whois.nic.ai', // إضافة خادم WHOIS لنطاق .ai
    ];

    return isset($whoisServers[$tld]) ? $whoisServers[$tld] : false;
}

function queryWhoisServer($whoisServer, $domain)
{
    $socket = @fsockopen($whoisServer, 43, $errno, $errstr, 30);
    if (!$socket) {
        error_log("Error connecting to WHOIS server ($whoisServer): $errno - $errstr");
        return false;
    }

    $queryFormats = [
        $domain . "\r\n",
        "domain " . $domain . "\r\n",
        "=" . $domain . "\r\n",
        "-d " . $domain . "\r\n"
    ];

    $response = '';
    foreach ($queryFormats as $query) {
        fwrite($socket, $query);
        $tempResponse = '';
        while (!feof($socket)) {
            $tempResponse .= fgets($socket, 128);
        }

        if (!empty($tempResponse)) {
            $response = $tempResponse;
            break;
        }

        fclose($socket);
        $socket = @fsockopen($whoisServer, 43, $errno, $errstr, 30);
        if (!$socket) {
            break;
        }
    }

    fclose($socket);

    if (empty($response)) {
        error_log("Empty response from WHOIS server for domain: $domain");
        return false;
    }

    $notFoundPatterns = [
        'No match', 'NOT FOUND', 'No entries found', 'No data found',
        'Domain not found', 'is free', 'is available'
    ];

    foreach ($notFoundPatterns as $pattern) {
        if (stripos($response, $pattern) !== false) {
            return "Domain $domain is not registered.";
        }
    }

    $usefulInfoPatterns = [
        'Registrar:', 'Registrant:', 'Admin:', 'Creation Date:', 'Updated Date:',
        'Registry Expiry Date:', 'Name Server:'
    ];

    $hasUsefulInfo = false;
    foreach ($usefulInfoPatterns as $pattern) {
        if (stripos($response, $pattern) !== false) {
            $hasUsefulInfo = true;
            break;
        }
    }

    if (!$hasUsefulInfo) {
        error_log("Response doesn't contain useful WHOIS information for domain: $domain");
        return false;
    }

    return $response;
}

function parseWhoisResult($whoisResult)
{
    $parsed = [];
    $lines = explode("\n", $whoisResult);

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || strpos($line, ':') === false) {
            continue;
        }

        list($key, $value) = explode(':', $line, 2);
        $key = trim($key);
        $value = trim($value);

        if (!empty($key) && !empty($value)) {
            if (!isset($parsed[$key])) {
                $parsed[$key] = $value;
            } else {
                if (!is_array($parsed[$key])) {
                    $parsed[$key] = [$parsed[$key]];
                }
                $parsed[$key][] = $value;
            }
        }
    }

    return $parsed;
}


function checkSSL($domain)
{
    $url = "https://" . $domain;
    $originalErrorReporting = error_reporting();
    error_reporting(0);
    $headers = get_headers($url, 1);
    error_reporting($originalErrorReporting);

    $sslInfo = [
        'secure' => false,
        'certInfo' => []
    ];

    if ($headers !== false && isset($headers[0]) && strpos($headers[0], '200') !== false) {
        $sslInfo['secure'] = true;
        $context = stream_context_create(["ssl" => ["capture_peer_cert" => true]]);
        $stream = stream_socket_client("ssl://{$domain}:443", $errno, $errstr, 30, STREAM_CLIENT_CONNECT, $context);

        if ($stream) {
            $params = stream_context_get_params($stream);
            if (isset($params["options"]["ssl"]["peer_certificate"])) {
                $cert = openssl_x509_parse($params["options"]["ssl"]["peer_certificate"]);
                $sslInfo['certInfo'] = [
                    'subject' => [
                        'commonName' => $cert['subject']['CN'] ?? 'Unknown',
                        'organizationName' => $cert['subject']['O'] ?? 'Unknown',
                        'organizationalUnitName' => $cert['subject']['OU'] ?? 'Unknown',
                        'countryName' => $cert['subject']['C'] ?? 'Unknown',
                        'stateOrProvinceName' => $cert['subject']['ST'] ?? 'Unknown',
                        'localityName' => $cert['subject']['L'] ?? 'Unknown',
                    ],
                    'issuer' => [
                        'commonName' => $cert['issuer']['CN'] ?? 'Unknown',
                        'organizationName' => $cert['issuer']['O'] ?? 'Unknown',
                        'organizationalUnitName' => $cert['issuer']['OU'] ?? 'Unknown',
                        'countryName' => $cert['issuer']['C'] ?? 'Unknown',
                        'stateOrProvinceName' => $cert['issuer']['ST'] ?? 'Unknown',
                        'localityName' => $cert['issuer']['L'] ?? 'Unknown',
                    ],
                    'validFrom' => date('Y-m-d H:i:s', $cert['validFrom_time_t']),
                    'validTo' => date('Y-m-d H:i:s', $cert['validTo_time_t']),
                    'serialNumber' => $cert['serialNumber'] ?? 'Unknown',
                    'signatureAlgorithm' => $cert['signatureTypeSN'] ?? 'Unknown',
                ];
            }
            fclose($stream); // Ensure the stream is closed
        }
    }

    return $sslInfo;
}

function formatWhoisResult($parsedResult, $responseTime, $domain, $sslInfo)
{
    $categories = [
        'Domain Information' => ['Domain Name', 'Registry Domain ID', 'Registrar WHOIS Server', 'Registrar URL', 'Updated Date', 'Creation Date', 'Registry Expiry Date', 'Registrar', 'Registrar IANA ID', 'Registrar Abuse Contact Email', 'Registrar Abuse Contact Phone', 'Domain Status', 'Name Server', 'DNSSEC'],
        'Registrant Information' => ['Registrant Name', 'Registrant Organization', 'Registrant Street', 'Registrant City', 'Registrant State/Province', 'Registrant Postal Code', 'Registrant Country', 'Registrant Phone', 'Registrant Email'],
        'Admin Information' => ['Admin Name', 'Admin Organization', 'Admin Street', 'Admin City', 'Admin State/Province', 'Admin Postal Code', 'Admin Country', 'Admin Phone', 'Admin Email'],
        'Tech Information' => ['Tech Name', 'Tech Organization', 'Tech Street', 'Tech City', 'Tech State/Province', 'Tech Postal Code', 'Tech Country', 'Tech Phone', 'Tech Email'],
        'Billing Information' => ['Billing Name', 'Billing Organization', 'Billing Street', 'Billing City', 'Billing State/Province', 'Billing Postal Code', 'Billing Country', 'Billing Phone', 'Billing Email'],
        'Other Information' => []
    ];

    $categorizedResults = [];
    foreach ($categories as $category => $fields) {
        $categorizedResults[$category] = [];
        foreach ($fields as $field) {
            if (isset($parsedResult[$field])) {
                $categorizedResults[$category][$field] = $parsedResult[$field];
                unset($parsedResult[$field]);
            }
        }
    }
    $categorizedResults['Other Information'] = $parsedResult;

    $formattedResult = '
    <div class="whois-result">
        <h2 class="domain-title">' . htmlspecialchars($domain) . '</h2>
        <div class="response-time">Response Time: ' . number_format($responseTime, 4) . ' seconds</div>
        <div class="ssl-info">
            <h3>SSL Information</h3>
            <p>Status: ' . ($sslInfo['secure'] ? '<span class="text-success">Secure (HTTPS)</span>' : '<span class="text-danger">Not Secure</span>') . '</p>';

    if ($sslInfo['secure'] && !empty($sslInfo['certInfo'])) {
        $formattedResult .= '
            <p>SSL Certificate:</p>
            <ul>
                <li>Issuer: ' . htmlspecialchars($sslInfo['certInfo']['issuer']['commonName']) . '</li>
                <li>Valid From: ' . htmlspecialchars($sslInfo['certInfo']['validFrom']) . '</li>
                <li>Valid To: ' . htmlspecialchars($sslInfo['certInfo']['validTo']) . '</li>
            </ul>';
    }

    $formattedResult .= '
        </div>
        <div class="accordion" id="whoisAccordion">';

    foreach ($categorizedResults as $category => $fields) {
        if (!empty($fields)) {
            $categoryId = str_replace(' ', '', $category);
            $formattedResult .= '
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading' . $categoryId . '">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse' . $categoryId . '" aria-expanded="true" aria-controls="collapse' . $categoryId . '">
                        ' . htmlspecialchars($category) . '
                    </button>
                </h2>
                <div id="collapse' . $categoryId . '" class="accordion-collapse collapse show" aria-labelledby="heading' . $categoryId . '" data-bs-parent="#whoisAccordion">
                    <div class="accordion-body">
                        <table class="table table-striped">
                            <tbody>';

            foreach ($fields as $key => $value) {
                $formattedResult .= '
                                <tr>
                                    <th>' . htmlspecialchars($key) . '</th>
                                    <td>' . (is_array($value) ? implode('<br>', array_map('htmlspecialchars', $value)) : htmlspecialchars($value)) . '</td>
                                </tr>';
            }

            $formattedResult .= '
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>';
        }
    }

    $formattedResult .= '
        </div>
        <button onclick="exportToCSV()" class="btn btn-primary mt-3">Export to CSV</button>
    </div>';

    $formattedResult .= '
    <style>
    .whois-result {
        font-family: Arial, sans-serif;
        margin: 0 auto;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .domain-title {
        text-align: center;
        color: #007bff;
        margin-bottom: 20px;
    }
    .response-time, .ssl-info {
        text-align: center;
        color: #6c757d;
        margin-bottom: 20px;
    }
    .ssl-info {
        background-color: #e9ecef;
        padding: 15px;
        border-radius: 5px;
        margin-top: 20px;
    }
    .accordion-button:not(.collapsed) {
        background-color: #e7f1ff;
        color: #0056b3;
    }
    .table {
        margin-bottom: 0;
    }
    .table th {
        width: 40%;
    }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function exportToCSV() {
        var csv = [];
        var tables = document.querySelectorAll(".whois-result table");

        csv.push("Domain: ' . $domain . '");
        csv.push("Response Time: ' . number_format($responseTime, 4) . ' seconds");
        csv.push("SSL Status: ' . ($sslInfo['secure'] ? 'Secure (HTTPS)' : 'Not Secure') . '");

        
        if (' . ($sslInfo['secure'] ? 'true' : 'false') . ') {
            csv.push("SSL Issuer: ' . ($sslInfo['secure'] ? $sslInfo['certInfo']['issuer']['commonName'] : '') . '");
            csv.push("SSL Valid From: ' . ($sslInfo['secure'] ? $sslInfo['certInfo']['validFrom'] : '') . '");
            csv.push("SSL Valid To: ' . ($sslInfo['secure'] ? $sslInfo['certInfo']['validTo'] : '') . '");
        }
        
        csv.push("");  // Empty line for separation

        tables.forEach(function(table) {
            var rows = table.querySelectorAll("tr");
            
            rows.forEach(function(row) {
                var rowData = [];
                var cols = row.querySelectorAll("th, td");
                
                cols.forEach(function(col) {
                    rowData.push(col.innerText.replace(/,/g, ";"));
                });
                
                csv.push(rowData.join(","));
            });
        });

        var csvFile = new Blob([csv.join("\\n")], {type: "text/csv"});
        var downloadLink = document.createElement("a");
        downloadLink.download = "whois_result.csv";
        downloadLink.href = window.URL.createObjectURL(csvFile);
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
    }
    </script>';

    return $formattedResult;
}