<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:12:33
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\seo-s\widdx-security-seo.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0d115164d9_41676574',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'ef74f2a773f19c0cc5eb60ebe566cfc1d4fe98af' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\seo-s\\widdx-security-seo.tpl',
      1 => 1749349593,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0d115164d9_41676574 (Smarty_Internal_Template $_smarty_tpl) {
?><section id="security" style="padding: 20px; margin-bottom: 20px; border: 1px solid #ddd; background: #fff;">
    <h4>Security</h4>
    <p>This section focuses on enhancing the security of your website to protect users and data. It includes:</p>
    <div class="row align-items-center">
        <div class="col-md-12 col-lg-12">
            <div class="accordion" id="accordionSecurity">

                <!-- SSL Certificate -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSecurityOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSecurityOne" aria-expanded="true" aria-controls="collapseSecurityOne">
                            <i class="fas 
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['sslCertificate'])===null||$tmp==='' ? 'Not Secure' : $tmp) == 'Not Secure') {?>
                                fa-times-circle text-danger
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>SSL Certificate
                        </button>
                    </h2>
                    <div id="collapseSecurityOne" class="accordion-collapse collapse show" aria-labelledby="headingSecurityOne" data-bs-parent="#accordionSecurity">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['sslCertificate'])===null||$tmp==='' ? 'Not Secure' : $tmp);?>
</div>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['sslCertificate'])===null||$tmp==='' ? 'Not Secure' : $tmp) == 'Not Secure') {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Install an SSL certificate to secure your website and improve SEO.</p>
                                <p class="text-danger">An SSL certificate encrypts data transfer between the user and your server, enhancing security and trust.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> Your site is using a valid SSL certificate, ensuring secure data transfer and improved trust.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Security Headers -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSecurityTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSecurityTwo" aria-expanded="false" aria-controls="collapseSecurityTwo">
                            <i class="fas 
                            <?php if (count((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['securityHeaders'])===null||$tmp==='' ? array() : $tmp)) < 4) {?>
                                fa-times-circle text-danger
                            <?php } elseif (count((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['securityHeaders'])===null||$tmp==='' ? array() : $tmp)) < 6) {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Security Headers
                        </button>
                    </h2>
                    <div id="collapseSecurityTwo" class="accordion-collapse collapse" aria-labelledby="headingSecurityTwo" data-bs-parent="#accordionSecurity">
                        <div class="accordion-body">
                            <ul>
                                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['securityHeaders'])===null||$tmp==='' ? array() : $tmp), 'value', false, 'header');
$_smarty_tpl->tpl_vars['value']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['header']->value => $_smarty_tpl->tpl_vars['value']->value) {
$_smarty_tpl->tpl_vars['value']->do_else = false;
?>
                                    <li><?php echo htmlspecialchars($_smarty_tpl->tpl_vars['header']->value, ENT_QUOTES, 'UTF-8', true);?>
: <?php echo htmlspecialchars($_smarty_tpl->tpl_vars['value']->value, ENT_QUOTES, 'UTF-8', true);?>
</li>
                                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                            </ul>
                            <?php if (count((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['securityHeaders'])===null||$tmp==='' ? array() : $tmp)) < 4) {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Not enough security headers are in place. Adding more headers can help protect your site from various types of attacks and vulnerabilities.</p>
                                <p class="text-danger">Implement more security headers to enhance your website's security.</p>
                            <?php } elseif (count((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['securityHeaders'])===null||$tmp==='' ? array() : $tmp)) < 6) {?>
                                <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Some security headers are present, but additional ones could improve your site's defense against attacks.</p>
                                <p class="text-warning">Consider adding more security headers for better protection.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> Your site has a good number of security headers implemented, providing robust protection against common threats.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Vulnerable Scripts -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSecurityThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSecurityThree" aria-expanded="false" aria-controls="collapseSecurityThree">
                            <i class="fas 
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['vulnerableScripts'])===null||$tmp==='' ? 'Unknown' : $tmp) != 'No known vulnerable scripts detected') {?>
                                fa-times-circle text-danger
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Vulnerable Scripts
                        </button>
                    </h2>
                    <div id="collapseSecurityThree" class="accordion-collapse collapse" aria-labelledby="headingSecurityThree" data-bs-parent="#accordionSecurity">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['vulnerableScripts'])===null||$tmp==='' ? 'Unknown' : $tmp);?>
</div>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['vulnerableScripts'])===null||$tmp==='' ? 'Unknown' : $tmp) != 'No known vulnerable scripts detected') {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Some scripts on your site are known to have vulnerabilities. Regularly update and patch all scripts to prevent exploitation.</p>
                                <p class="text-danger">Update or remove the detected vulnerable scripts immediately.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> No known vulnerable scripts were detected. Regular updates and patches are still recommended to maintain security.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Content Security Policy -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSecurityFour">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSecurityFour" aria-expanded="false" aria-controls="collapseSecurityFour">
                            <i class="fas 
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['contentSecurityPolicy'])===null||$tmp==='' ? 'Not set' : $tmp) == 'Not set') {?>
                                fa-times-circle text-danger
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Content Security Policy
                        </button>
                    </h2>
                    <div id="collapseSecurityFour" class="accordion-collapse collapse" aria-labelledby="headingSecurityFour" data-bs-parent="#accordionSecurity">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['contentSecurityPolicy'])===null||$tmp==='' ? 'Not set' : $tmp);?>
</div>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['contentSecurityPolicy'])===null||$tmp==='' ? 'Not set' : $tmp) == 'Not set') {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> A Content Security Policy (CSP) is not set. Implementing a CSP can significantly enhance your site's security against various types of attacks.</p>
                                <p class="text-danger">Implement a Content Security Policy to prevent XSS attacks and other injections.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> A Content Security Policy is in place, which helps prevent XSS attacks and enhances site security.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Safe Browsing -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingSecurityFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSecurityFive" aria-expanded="false" aria-controls="collapseSecurityFive">
                            <i class="fas 
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['safeBrowsing'])===null||$tmp==='' ? 'Unsafe' : $tmp) == 'Unsafe') {?>
                                fa-times-circle text-danger
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Safe Browsing
                        </button>
                    </h2>
                    <div id="collapseSecurityFive" class="accordion-collapse collapse" aria-labelledby="headingSecurityFive" data-bs-parent="#accordionSecurity">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['safeBrowsing'])===null||$tmp==='' ? 'Unsafe' : $tmp);?>
</div>
                            <?php if ((($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['security']['safeBrowsing'])===null||$tmp==='' ? 'Unsafe' : $tmp) == 'Unsafe') {?>
                                <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> Your site is not safe for browsing. Take immediate actions to ensure it is secure and free from threats.</p>
                            <?php } else { ?>
                                <p class="text-success"><i class="fas fa-check-circle text-success"></i> Your site is safe for browsing, providing a secure experience for users.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section><?php }
}
