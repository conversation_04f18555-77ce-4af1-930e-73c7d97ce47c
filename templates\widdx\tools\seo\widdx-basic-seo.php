<?php
/**
 * Basic SEO Analysis Module
 * Handles all basic SEO checks and data processing
 */

class WiddxBasicSeo {
    private $analysisResult;
    private $url;
    private $allowedDomains = ['localhost', '127.0.0.1', '::1']; // Add allowed domains for security
    
    public function __construct($url) {
        $this->url = $this->validateUrl($url);
        $this->analysisResult = [
            'basicSeo' => [
                'title' => '',
                'metaDescription' => '',
                'keywords' => '',
                'h1Tags' => [],
                'h2Tags' => [],
                'imagesWithoutAlt' => [],
                'linkRatio' => [
                    'internal' => 0,
                    'external' => 0,
                    'total' => 0,
                    'ratio' => 0
                ],
                // Status information for template
                'titleStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'metaDescriptionStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'keywordsStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'h1TagsStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'h2TagsStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'imagesStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ],
                'linkRatioStatus' => [
                    'icon' => '',
                    'class' => '',
                    'message' => '',
                    'details' => ''
                ]
            ]
        ];
    }

    public function analyze() {
        $this->analyzeTitle();
        $this->analyzeMetaDescription();
        $this->analyzeKeywords();
        $this->analyzeHeadings();
        $this->analyzeImages();
        $this->analyzeLinks();
        
        return $this->analysisResult;
    }

    private function analyzeTitle() {
        $title = $this->getPageTitle();
        $this->analysisResult['basicSeo']['title'] = $title;
        
        $titleLength = strlen($title);
        if ($titleLength > 60 || $titleLength < 30) {
            $this->analysisResult['basicSeo']['titleStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => $titleLength > 60 ? 'SEO title is too long. Shorten it to 60 characters or fewer.' : 'SEO title is too short. Lengthen it to between 30-60 characters.',
                'details' => $titleLength > 60 ? 'A concise title helps ensure it displays fully in search results and improves user engagement.' : 'A longer title with relevant keywords can enhance search visibility and click-through rates.'
            ];
        } else {
            $this->analysisResult['basicSeo']['titleStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'SEO title length is good. Ensure it includes your target keywords and encourages clicks.',
                'details' => 'Your title is well-optimized. It should clearly reflect the page content and include relevant keywords.'
            ];
        }
    }

    private function analyzeMetaDescription() {
        $metaDescription = $this->getMetaDescription();
        $this->analysisResult['basicSeo']['metaDescription'] = $metaDescription;
        
        $metaLength = strlen($metaDescription);
        if ($metaLength > 160 || $metaLength < 50) {
            $this->analysisResult['basicSeo']['metaDescriptionStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => $metaLength > 160 ? 'Meta description is too long. Shorten it to 160 characters or fewer.' : 'Meta description is too short. Lengthen it to between 50-160 characters.',
                'details' => $metaLength > 160 ? 'A well-crafted meta description can boost click-through rates.' : 'A longer meta description provides a better summary and can entice users to click.'
            ];
        } else {
            $this->analysisResult['basicSeo']['metaDescriptionStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'Meta description length is good. Ensure it is compelling and includes keywords.',
                'details' => 'Your meta description is well-optimized. It should accurately summarize the page content.'
            ];
        }
    }

    private function analyzeKeywords() {
        $keywords = $this->getKeywords();
        $this->analysisResult['basicSeo']['keywords'] = $keywords;
        
        if (empty($keywords)) {
            $this->analysisResult['basicSeo']['keywordsStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => 'Keywords are missing. Consider adding relevant keywords.',
                'details' => 'Including keywords can assist with optimization, though it\'s less crucial now.'
            ];
        } else {
            $this->analysisResult['basicSeo']['keywordsStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'Keywords are present. Use them strategically throughout your content.',
                'details' => 'Your keywords are present. Ensure they are used naturally throughout your content.'
            ];
        }
    }

    private function analyzeHeadings() {
        $h1Tags = $this->getH1Tags();
        $h2Tags = $this->getH2Tags();
        
        $this->analysisResult['basicSeo']['h1Tags'] = $h1Tags;
        $this->analysisResult['basicSeo']['h2Tags'] = $h2Tags;
        
        // H1 Tags Status
        if (count($h1Tags) == 0) {
            $this->analysisResult['basicSeo']['h1TagsStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => 'No H1 tags found. Add an H1 tag to your page.',
                'details' => 'An H1 tag is important for content structure and SEO.'
            ];
        } elseif (count($h1Tags) > 1) {
            $this->analysisResult['basicSeo']['h1TagsStatus'] = [
                'icon' => 'fa-exclamation-circle text-warning',
                'class' => 'text-warning',
                'message' => 'Multiple H1 tags found. Use only one H1 tag per page.',
                'details' => 'Multiple H1 tags can confuse search engines.'
            ];
        } else {
            $this->analysisResult['basicSeo']['h1TagsStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'H1 tag usage is good. Use H1 tags to define the main topic.',
                'details' => 'Your use of H1 tags is appropriate.'
            ];
        }
        
        // H2 Tags Status
        if (count($h2Tags) == 0) {
            $this->analysisResult['basicSeo']['h2TagsStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => 'No H2 tags found. Add H2 tags to structure your content.',
                'details' => 'H2 tags help break up content and improve readability.'
            ];
        } else {
            $this->analysisResult['basicSeo']['h2TagsStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'H2 tag usage is good. Use H2 tags to organize subsections.',
                'details' => 'Your H2 tags are used effectively.'
            ];
        }
    }

    private function analyzeImages() {
        $imagesWithoutAlt = $this->getImagesWithoutAlt();
        $this->analysisResult['basicSeo']['imagesWithoutAlt'] = $imagesWithoutAlt;
        
        if (empty($imagesWithoutAlt)) {
            $this->analysisResult['basicSeo']['imagesStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'All images have alt text. Good job!',
                'details' => 'Your images are properly optimized for SEO and accessibility.'
            ];
        } else {
            $this->analysisResult['basicSeo']['imagesStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => 'Some images are missing alt text. This can negatively impact your SEO and accessibility.',
                'details' => 'Add descriptive alt text to improve accessibility and SEO.'
            ];
        }
    }

    private function analyzeLinks() {
        $links = $this->getLinkAnalysis();
        $this->analysisResult['basicSeo']['linkRatio'] = $links;
        
        if ($links['internal'] == 0 || $links['external'] == 0) {
            $this->analysisResult['basicSeo']['linkRatioStatus'] = [
                'icon' => 'fa-times-circle text-danger',
                'class' => 'text-danger',
                'message' => $links['internal'] == 0 ? 'No internal links found. Add internal links to improve site structure.' : 'No external links found. Consider adding high-quality external links.',
                'details' => $links['internal'] == 0 ? 'Internal links help distribute page authority and enhance user navigation.' : 'External links to authoritative sites can boost credibility and provide additional value.'
            ];
        } else {
            $this->analysisResult['basicSeo']['linkRatioStatus'] = [
                'icon' => 'fa-check-circle text-success',
                'class' => 'text-success',
                'message' => 'Link ratio is good. Maintain a healthy balance of internal and external links.',
                'details' => 'Your link ratio is well-balanced. Continue managing internal and external links effectively.'
            ];
        }
    }

    // Security validation method
    private function validateUrl($url) {
        // Basic URL validation
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            throw new InvalidArgumentException('Invalid URL provided');
        }
        
        $parsedUrl = parse_url($url);
        $host = $parsedUrl['host'] ?? '';
        
        // Check if domain is in allowed list (for development/testing)
        if (!empty($this->allowedDomains) && !in_array($host, $this->allowedDomains)) {
            // For production, you might want to allow specific domains or patterns
            // For now, we'll allow external domains but with security restrictions
        }
        
        // Prevent access to private IP ranges
        $ip = gethostbyname($host);
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            throw new InvalidArgumentException('Access to private/reserved IP ranges is not allowed');
        }
        
        return $url;
    }
    
    // Secure HTTP client method using cURL
    private function fetchContent($url) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => false, // Prevent redirect attacks
            CURLOPT_TIMEOUT => 10, // 10 second timeout
            CURLOPT_CONNECTTIMEOUT => 5, // 5 second connection timeout
            CURLOPT_USERAGENT => 'WIDDX SEO Analyzer/1.0',
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_MAXREDIRS => 0, // No redirects
            CURLOPT_PROTOCOLS => CURLPROTO_HTTP | CURLPROTO_HTTPS, // Only HTTP/HTTPS
            CURLOPT_REDIR_PROTOCOLS => CURLPROTO_HTTP | CURLPROTO_HTTPS,
            CURLOPT_MAXFILESIZE => 1024 * 1024 * 2, // 2MB max file size
        ]);
        
        $content = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($content === false || !empty($error)) {
            error_log("cURL Error: " . $error);
            return false;
        }
        
        if ($httpCode !== 200) {
            error_log("HTTP Error: " . $httpCode);
            return false;
        }
        
        return $content;
    }

    // Helper methods for actual implementation
    private function getPageTitle() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return '';
        }
        
        preg_match('/<title>(.*?)<\/title>/i', $html, $matches);
        return isset($matches[1]) ? trim($matches[1]) : '';
    }

    private function getMetaDescription() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return '';
        }
        
        preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\'](.*?)["\']/i', $html, $matches);
        return isset($matches[1]) ? trim($matches[1]) : '';
    }

    private function getKeywords() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return '';
        }
        
        preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\'](.*?)["\']/i', $html, $matches);
        return isset($matches[1]) ? trim($matches[1]) : '';
    }

    private function getH1Tags() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return [];
        }
        
        preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $html, $matches);
        return array_map('trim', $matches[1]);
    }

    private function getH2Tags() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return [];
        }
        
        preg_match_all('/<h2[^>]*>(.*?)<\/h2>/i', $html, $matches);
        return array_map('trim', $matches[1]);
    }

    private function getImagesWithoutAlt() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return [];
        }
        
        $images = [];
        preg_match_all('/<img[^>]*>/i', $html, $matches);
        
        foreach ($matches[0] as $imgTag) {
            if (!preg_match('/alt=["\'](.*?)["\']/i', $imgTag)) {
                preg_match('/src=["\'](.*?)["\']/i', $imgTag, $src);
                $images[] = [
                    'name' => basename($src[1] ?? ''),
                    'src' => $src[1] ?? ''
                ];
            }
        }
        
        return $images;
    }

    private function getLinkAnalysis() {
        $html = $this->fetchContent($this->url);
        if ($html === false) {
            return [
                'internal' => 0,
                'external' => 0,
                'total' => 0,
                'ratio' => 0
            ];
        }
        
        $internal = 0;
        $external = 0;
        $domain = parse_url($this->url, PHP_URL_HOST);
        
        preg_match_all('/<a[^>]*href=["\'](.*?)["\'][^>]*>/i', $html, $matches);
        
        foreach ($matches[1] as $link) {
            if (empty($link) || $link === '#' || strpos($link, 'javascript:') === 0) {
                continue;
            }
            
            $linkDomain = parse_url($link, PHP_URL_HOST);
            if ($linkDomain === $domain || empty($linkDomain)) {
                $internal++;
            } else {
                $external++;
            }
        }
        
        $total = $internal + $external;
        $ratio = $total > 0 ? round(($internal / $total) * 100, 2) : 0;
        
        return [
            'internal' => $internal,
            'external' => $external,
            'total' => $total,
            'ratio' => $ratio
        ];
    }
}