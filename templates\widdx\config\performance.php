<?php
/**
 * WIDDX Performance Configuration
 * Optimizations for better performance and caching
 */

// Prevent direct access
if (!defined('WHMCS')) {
    die('Access denied');
}

class WiddxPerformance {
    
    private static $cacheDir = null;
    private static $cacheEnabled = true;
    
    /**
     * Initialize performance optimizations
     */
    public static function initialize() {
        self::$cacheDir = __DIR__ . '/../cache/';
        self::ensureCacheDirectory();
        self::setPerformanceHeaders();
        self::enableOutputCompression();
    }
    
    /**
     * Ensure cache directory exists and is protected
     */
    private static function ensureCacheDirectory() {
        if (!is_dir(self::$cacheDir)) {
            mkdir(self::$cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache files
        $htaccessFile = self::$cacheDir . '.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
        
        // Create index.php to prevent directory listing
        $indexFile = self::$cacheDir . 'index.php';
        if (!file_exists($indexFile)) {
            file_put_contents($indexFile, "<?php\n// Cache directory - Access denied\ndie('Access denied');");
        }
    }
    
    /**
     * Set performance-related headers
     */
    private static function setPerformanceHeaders() {
        if (!headers_sent()) {
            // Enable browser caching for static assets
            $extension = pathinfo($_SERVER['REQUEST_URI'] ?? '', PATHINFO_EXTENSION);
            
            switch ($extension) {
                case 'css':
                case 'js':
                    header('Cache-Control: public, max-age=31536000'); // 1 year
                    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
                    break;
                    
                case 'png':
                case 'jpg':
                case 'jpeg':
                case 'gif':
                case 'webp':
                case 'svg':
                    header('Cache-Control: public, max-age=2592000'); // 30 days
                    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 2592000) . ' GMT');
                    break;
                    
                case 'woff':
                case 'woff2':
                case 'ttf':
                case 'eot':
                    header('Cache-Control: public, max-age=31536000'); // 1 year
                    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
                    break;
                    
                default:
                    // For HTML and other dynamic content
                    header('Cache-Control: public, max-age=300'); // 5 minutes
                    break;
            }
            
            // Add ETag for better caching
            if (isset($_SERVER['REQUEST_URI'])) {
                $etag = md5($_SERVER['REQUEST_URI'] . filemtime(__FILE__));
                header('ETag: "' . $etag . '"');
                
                // Check if client has cached version
                if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && $_SERVER['HTTP_IF_NONE_MATCH'] === '"' . $etag . '"') {
                    http_response_code(304);
                    exit;
                }
            }
        }
    }
    
    /**
     * Enable output compression
     */
    private static function enableOutputCompression() {
        if (!ob_get_level() && extension_loaded('zlib') && !ini_get('zlib.output_compression')) {
            ob_start('ob_gzhandler');
        }
    }
    
    /**
     * Cache data with expiration
     */
    public static function cache($key, $data, $expiration = 3600) {
        if (!self::$cacheEnabled) {
            return false;
        }
        
        $cacheFile = self::$cacheDir . md5($key) . '.cache';
        $cacheData = [
            'data' => $data,
            'expiration' => time() + $expiration,
            'created' => time()
        ];
        
        return file_put_contents($cacheFile, serialize($cacheData), LOCK_EX) !== false;
    }
    
    /**
     * Retrieve cached data
     */
    public static function getCache($key) {
        if (!self::$cacheEnabled) {
            return null;
        }
        
        $cacheFile = self::$cacheDir . md5($key) . '.cache';
        
        if (!file_exists($cacheFile)) {
            return null;
        }
        
        $cacheData = unserialize(file_get_contents($cacheFile));
        
        if (!$cacheData || !isset($cacheData['expiration'])) {
            return null;
        }
        
        // Check if cache has expired
        if (time() > $cacheData['expiration']) {
            unlink($cacheFile);
            return null;
        }
        
        return $cacheData['data'];
    }
    
    /**
     * Clear specific cache
     */
    public static function clearCache($key) {
        $cacheFile = self::$cacheDir . md5($key) . '.cache';
        
        if (file_exists($cacheFile)) {
            return unlink($cacheFile);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public static function clearAllCache() {
        $files = glob(self::$cacheDir . '*.cache');
        $cleared = 0;
        
        foreach ($files as $file) {
            if (unlink($file)) {
                $cleared++;
            }
        }
        
        return $cleared;
    }
    
    /**
     * Clean expired cache files
     */
    public static function cleanExpiredCache() {
        $files = glob(self::$cacheDir . '*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $cacheData = unserialize(file_get_contents($file));
            
            if (!$cacheData || !isset($cacheData['expiration']) || time() > $cacheData['expiration']) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache statistics
     */
    public static function getCacheStats() {
        $files = glob(self::$cacheDir . '*.cache');
        $totalSize = 0;
        $totalFiles = count($files);
        $expiredFiles = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $cacheData = unserialize(file_get_contents($file));
            if (!$cacheData || !isset($cacheData['expiration']) || time() > $cacheData['expiration']) {
                $expiredFiles++;
            }
        }
        
        return [
            'total_files' => $totalFiles,
            'total_size' => $totalSize,
            'total_size_formatted' => self::formatBytes($totalSize),
            'expired_files' => $expiredFiles,
            'cache_directory' => self::$cacheDir,
            'cache_enabled' => self::$cacheEnabled
        ];
    }
    
    /**
     * Format bytes to human readable format
     */
    private static function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Minify CSS content
     */
    public static function minifyCSS($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove unnecessary spaces around specific characters
        $css = str_replace([' {', '{ ', ' }', '} ', ': ', ' :', '; ', ' ;', ', ', ' ,'], ['{', '{', '}', '}', ':', ':', ';', ';', ',', ','], $css);
        
        return trim($css);
    }
    
    /**
     * Minify JavaScript content
     */
    public static function minifyJS($js) {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remove spaces around operators and punctuation
        $js = preg_replace('/\s*([{}();,=+\-*\/])\s*/', '$1', $js);
        
        return trim($js);
    }
    
    /**
     * Combine and minify CSS files
     */
    public static function combineCSSFiles($files, $outputFile) {
        $combinedCSS = '';
        $lastModified = 0;
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $combinedCSS .= file_get_contents($file) . "\n";
                $lastModified = max($lastModified, filemtime($file));
            }
        }
        
        // Check if output file exists and is newer than source files
        if (file_exists($outputFile) && filemtime($outputFile) >= $lastModified) {
            return true; // Already up to date
        }
        
        $minifiedCSS = self::minifyCSS($combinedCSS);
        
        // Add source map comment
        $minifiedCSS .= "\n/* Combined and minified CSS - Generated: " . date('Y-m-d H:i:s') . " */";
        
        return file_put_contents($outputFile, $minifiedCSS) !== false;
    }
    
    /**
     * Combine and minify JavaScript files
     */
    public static function combineJSFiles($files, $outputFile) {
        $combinedJS = '';
        $lastModified = 0;
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                $combinedJS .= file_get_contents($file) . ";\n";
                $lastModified = max($lastModified, filemtime($file));
            }
        }
        
        // Check if output file exists and is newer than source files
        if (file_exists($outputFile) && filemtime($outputFile) >= $lastModified) {
            return true; // Already up to date
        }
        
        $minifiedJS = self::minifyJS($combinedJS);
        
        // Add source map comment
        $minifiedJS .= "\n/* Combined and minified JS - Generated: " . date('Y-m-d H:i:s') . " */";
        
        return file_put_contents($outputFile, $minifiedJS) !== false;
    }
    
    /**
     * Optimize images (basic optimization)
     */
    public static function optimizeImage($imagePath, $quality = 85) {
        if (!file_exists($imagePath)) {
            return false;
        }
        
        $imageInfo = getimagesize($imagePath);
        if (!$imageInfo) {
            return false;
        }
        
        $mimeType = $imageInfo['mime'];
        
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                if ($image) {
                    imagejpeg($image, $imagePath, $quality);
                    imagedestroy($image);
                    return true;
                }
                break;
                
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                if ($image) {
                    // PNG compression level (0-9)
                    $compression = 9 - round(($quality / 100) * 9);
                    imagepng($image, $imagePath, $compression);
                    imagedestroy($image);
                    return true;
                }
                break;
        }
        
        return false;
    }
    
    /**
     * Enable/disable caching
     */
    public static function setCacheEnabled($enabled) {
        self::$cacheEnabled = (bool) $enabled;
    }
    
    /**
     * Check if caching is enabled
     */
    public static function isCacheEnabled() {
        return self::$cacheEnabled;
    }
    
    /**
     * Preload critical resources
     */
    public static function preloadCriticalResources($resources) {
        if (!headers_sent()) {
            foreach ($resources as $resource) {
                $type = $resource['type'] ?? 'script';
                $url = $resource['url'] ?? '';
                $crossorigin = $resource['crossorigin'] ?? '';
                
                if (!empty($url)) {
                    $header = "Link: <{$url}>; rel=preload; as={$type}";
                    if (!empty($crossorigin)) {
                        $header .= "; crossorigin={$crossorigin}";
                    }
                    header($header, false);
                }
            }
        }
    }
    
    /**
     * Set up resource hints
     */
    public static function setResourceHints($hints) {
        if (!headers_sent()) {
            foreach ($hints as $hint) {
                $rel = $hint['rel'] ?? 'dns-prefetch';
                $href = $hint['href'] ?? '';
                
                if (!empty($href)) {
                    header("Link: <{$href}>; rel={$rel}", false);
                }
            }
        }
    }
}

// Initialize performance optimizations
WiddxPerformance::initialize();