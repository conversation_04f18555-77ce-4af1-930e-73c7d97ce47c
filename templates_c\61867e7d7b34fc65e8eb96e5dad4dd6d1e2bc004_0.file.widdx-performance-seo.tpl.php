<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:12:33
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\seo-s\widdx-performance-seo.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0d110ec581_83135944',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '61867e7d7b34fc65e8eb96e5dad4dd6d1e2bc004' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\seo-s\\widdx-performance-seo.tpl',
      1 => 1749349593,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0d110ec581_83135944 (Smarty_Internal_Template $_smarty_tpl) {
?><section id="performance" style="padding: 20px; margin-bottom: 20px; border: 1px solid #ddd; background: #fff;">
    <h4>Performance</h4>
    <p>This section focuses on improving your website's load speed and overall performance. Key aspects include:</p>
    <div class="row align-items-center">
        <div class="col-md-12 col-lg-12">
            <div class="accordion" id="accordionPerformance">

                <!-- Image Expires Headers -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceOne" aria-expanded="true" aria-controls="collapsePerformanceOne">
                            <i class="fas 
                            <?php if (strpos($_smarty_tpl->tpl_vars['seoResult']->value['performance']['imageExpiresHeaders'],'0/') === false) {?>
                                fa-check-circle text-success
                            <?php } elseif (strpos($_smarty_tpl->tpl_vars['seoResult']->value['performance']['imageExpiresHeaders'],'0/') === 0) {?>
                                fa-times-circle text-danger
                            <?php } else { ?>
                                fa-exclamation-circle text-warning
                            <?php }?> mr-2"></i>Image Expires Headers
                        </button>
                    </h2>
                    <div id="collapsePerformanceOne" class="accordion-collapse collapse show" aria-labelledby="headingPerformanceOne" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['imageExpiresHeaders'];?>
</div>
                            <?php if (strpos($_smarty_tpl->tpl_vars['seoResult']->value['performance']['imageExpiresHeaders'],'0/') === 0) {?>
                                <p class="text-danger">Add expires headers to your images to improve page load speed.</p>
                                <p class="text-danger">Expires headers help cache your images for faster page loading. Consider adding them to all images.</p>
                            <?php } elseif (strpos($_smarty_tpl->tpl_vars['seoResult']->value['performance']['imageExpiresHeaders'],'0/') !== false) {?>
                                <p class="text-warning">Ensure all images have expires headers set.</p>
                                <p class="text-warning">Some images have expires headers, but others might be missing. Ensure consistency across all images.</p>
                            <?php } else { ?>
                                <p class="text-success">All images have expires headers set, which helps in improving load times.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Unminified JS -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceTwo" aria-expanded="false" aria-controls="collapsePerformanceTwo">
                            <i class="fas 
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedJs'] == 'All Not Minified') {?>
                                fa-times-circle text-danger
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedJs'] == 'Some Unminified') {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Unminified JS
                        </button>
                    </h2>
                    <div id="collapsePerformanceTwo" class="accordion-collapse collapse" aria-labelledby="headingPerformanceTwo" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedJs'];?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedJs'] == 'All Not Minified') {?>
                                <p class="text-danger">Minify all JavaScript files to enhance performance.</p>
                                <p class="text-danger">None of the JavaScript files are minified. Minifying your JavaScript files can significantly reduce load times.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedJs'] == 'Some Unminified') {?>
                                <p class="text-warning">Minify all JavaScript files to improve page load speed.</p>
                                <p class="text-warning">Some JavaScript files are not minified. Minification reduces file size and improves load times.</p>
                            <?php } else { ?>
                                <p class="text-success">All JavaScript files are minified, improving page load speed.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Unminified CSS -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceThree" aria-expanded="false" aria-controls="collapsePerformanceThree">
                            <i class="fas 
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedCss'] == 'All Not Minified') {?>
                                fa-times-circle text-danger
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedCss'] == 'Some Unminified') {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Unminified CSS
                        </button>
                    </h2>
                    <div id="collapsePerformanceThree" class="accordion-collapse collapse" aria-labelledby="headingPerformanceThree" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedCss'];?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedCss'] == 'All Not Minified') {?>
                                <p class="text-danger">Minify all CSS files to enhance performance.</p>
                                <p class="text-danger">None of the CSS files are minified. Consider minifying your CSS to improve page load speed.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['unminifiedCss'] == 'Some Unminified') {?>
                                <p class="text-warning">Minify all CSS files to improve page load speed.</p>
                                <p class="text-warning">Some CSS files are not minified. Minification can help reduce the file size and improve load times.</p>
                            <?php } else { ?>
                                <p class="text-success">All CSS files are minified, which helps in reducing the load time.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Page Objects -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceFour">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceFour" aria-expanded="false" aria-controls="collapsePerformanceFour">
                            <i class="fas 
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['total'] > 100) {?>
                                fa-times-circle text-danger
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['total'] > 50) {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Page Objects
                        </button>
                    </h2>
                    <div id="collapsePerformanceFour" class="accordion-collapse collapse" aria-labelledby="headingPerformanceFour" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <ul>
                                <li>Images: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['images'];?>
</li>
                                <li>Scripts: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['scripts'];?>
</li>
                                <li>Stylesheets: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['stylesheets'];?>
</li>
                                <li>Other: <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['other'];?>
</li>
                            </ul>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['total'] > 100) {?>
                                <p class="text-danger">Consider reducing the number of page objects to improve load time.</p>
                                <p class="text-danger">A high number of page objects can slow down loading times. Optimize the page by reducing unnecessary objects.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageObjects']['total'] > 50) {?>
                                <p class="text-warning">Review the number of page objects for potential optimization.</p>
                                <p class="text-warning">The number of page objects is somewhat high. Consider optimizing to improve load times.</p>
                            <?php } else { ?>
                                <p class="text-success">The number of page objects is within an acceptable range, which is good for performance.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Page Size -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceFive" aria-expanded="false" aria-controls="collapsePerformanceFive">
                            <i class="fas 
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageSize'] > 5000000) {?>
                                fa-times-circle text-danger
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageSize'] > 2000000) {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Page Size
                        </button>
                    </h2>
                    <div id="collapsePerformanceFive" class="accordion-collapse collapse" aria-labelledby="headingPerformanceFive" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo number_format($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageSize']);?>
 bytes</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageSize'] > 5000000) {?>
                                <p class="text-danger">Your page is over 5MB. Reduce page size to improve load times.</p>
                                <p class="text-danger">Large page sizes can negatively impact load times. Optimize images and resources to reduce the size.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['pageSize'] > 2000000) {?>
                                <p class="text-warning">Your page is over 2MB. Consider optimizing to improve load times.</p>
                                <p class="text-warning">Consider reducing the size of your page to enhance performance. Minimize resources and optimize images.</p>
                            <?php } else { ?>
                                <p class="text-success">The page size is acceptable, contributing to better performance.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Response Time -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingPerformanceSix">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapsePerformanceSix" aria-expanded="false" aria-controls="collapsePerformanceSix">
                            <i class="fas 
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['responseTime'] > 1) {?>
                                fa-times-circle text-danger
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['responseTime'] > 0.5) {?>
                                fa-exclamation-circle text-warning
                            <?php } else { ?>
                                fa-check-circle text-success
                            <?php }?> mr-2"></i>Response Time
                        </button>
                    </h2>
                    <div id="collapsePerformanceSix" class="accordion-collapse collapse" aria-labelledby="headingPerformanceSix" data-bs-parent="#accordionPerformance">
                        <div class="accordion-body">
                            <div class="alert alert-info"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['performance']['responseTime'];?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['responseTime'] > 1) {?>
                                <p class="text-danger">Your server response time is over 1 second. Optimize server performance.</p>
                                <p class="text-danger">Slow server response times can affect overall page load. Consider improving server efficiency.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['performance']['responseTime'] > 0.5) {?>
                                <p class="text-warning">Your server response time is over 0.5 seconds. Consider optimization.</p>
                                <p class="text-warning">Response times over 0.5 seconds might need attention. Optimize server settings for better performance.</p>
                            <?php } else { ?>
                                <p class="text-success">The server response time is optimal, contributing to fast page load times.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</section><?php }
}
