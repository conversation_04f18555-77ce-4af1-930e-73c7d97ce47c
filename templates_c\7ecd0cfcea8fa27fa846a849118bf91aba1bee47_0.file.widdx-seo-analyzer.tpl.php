<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:11:36
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\widdx-seo-analyzer.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0cd8c35580_45567064',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '7ecd0cfcea8fa27fa846a849118bf91aba1bee47' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\widdx-seo-analyzer.tpl',
      1 => 1749680011,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0cd8c35580_45567064 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'C:\\xampp\\htdocs\\Whmcs\\vendor\\smarty\\smarty\\libs\\plugins\\modifier.replace.php','function'=>'smarty_modifier_replace',),1=>array('file'=>'C:\\xampp\\htdocs\\Whmcs\\vendor\\smarty\\smarty\\libs\\plugins\\modifier.regex_replace.php','function'=>'smarty_modifier_regex_replace',),));
if (!$_smarty_tpl->tpl_vars['seoResult']->value) {?>
    <!-- SEO Analyzer Section -->
    <section class="p-5">
        <div class="container my-5">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-4"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['seo_analyzer_by_widdx'];?>
</h1>
                <p class="lead"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['seo_analyzer_intro'];?>
</p>
            </div>

            <!-- Form -->
            <div class="row justify-content-center mb-5">
                <div class="col-lg-8 col-md-10">
                    <div class="card shadow-lg border-primary mb-4">
                        <div class="card-body">
                            <form method="get" action="<?php echo $_smarty_tpl->tpl_vars['formAction']->value;?>
" id="seoForm" class="input-group input-group-lg">
                                <input type="text" id="url" name="url" class="form-control" required
                                    placeholder="<?php echo $_smarty_tpl->tpl_vars['LANG']->value['enter_website_url'];?>
">
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-primary"><i class="fas fa-search mr-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['analyze_now'];?>
</button>
                                </div>
                                <input type="hidden" name="page" value="seo-analyzer">
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Why Choose -->
            <div class="text-center mb-5">
                <h3><?php echo $_smarty_tpl->tpl_vars['LANG']->value['why_choose_seo_analyzer'];?>
</h3>
            </div>
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-rocket text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['boost_performance'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['boost_performance_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-chart-line text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['uncover_opportunities'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['uncover_opportunities_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-users text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['attract_convert'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['attract_convert_desc'];?>
</p>
                    </div>
                </div>
            </div>

            <!-- Additional Features -->
            <div class="text-center mb-5">
                <h3><?php echo $_smarty_tpl->tpl_vars['LANG']->value['additional_features'];?>
</h3>
            </div>
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-tools text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['easy_to_use'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['easy_to_use_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-shield-alt text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['secure'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['secure_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-headset text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['support_24_7'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['support_24_7_desc'];?>
</p>
                    </div>
                </div>
            </div>

            <!-- More Benefits -->
            <div class="text-center mb-5">
                <h3><?php echo $_smarty_tpl->tpl_vars['LANG']->value['more_benefits_seo'];?>
</h3>
            </div>
            <div class="row text-center">
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-cogs text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['technical_seo_insights'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['technical_seo_insights_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-file-alt text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['comprehensive_reports'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['comprehensive_reports_desc'];?>
</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="p-4 border rounded shadow-sm">
                        <i class="fas fa-lightbulb text-primary fa-3x mb-3"></i>
                        <h5><?php echo $_smarty_tpl->tpl_vars['LANG']->value['actionable_recommendations'];?>
</h5>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['actionable_recommendations_desc'];?>
</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQs Section -->
    <section id="faq" class="ptb-100">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-9 col-lg-8">
                    <div class="section-heading text-center mb-5">
                        <h2><?php echo $_smarty_tpl->tpl_vars['LANG']->value['faq_seo_analyzer'];?>
</h2>
                        <p><?php echo $_smarty_tpl->tpl_vars['LANG']->value['faq_seo_analyzer_desc'];?>
</p>
                    </div>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-md-12 col-lg-12">
                    <div class="accordion" id="accordionExample">
                        <!-- Item 1 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['what_is_widdx'];?>

                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_answer_1'];?>

                                </div>
                            </div>
                        </div>

                        <!-- Item 2 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['how_to_use_widdx'];?>

                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_answer_2'];?>

                                </div>
                            </div>
                        </div>

                        <!-- Item 3 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['multiple_site_analysis'];?>

                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_answer_3'];?>

                                </div>
                            </div>
                        </div>

                        <!-- Item 4 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFour">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_cost'];?>

                                </button>
                            </h2>
                            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_answer_4'];?>

                                </div>
                            </div>
                        </div>

                        <!-- Item 5 -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFive">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_support'];?>

                                </button>
                            </h2>
                            <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive"
                                data-bs-parent="#accordionExample">
                                <div class="accordion-body">
                                    <?php echo $_smarty_tpl->tpl_vars['LANG']->value['widdx_answer_5'];?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Professional Loading Spinner Overlay -->
    <div id="loadingSpinner" class="professional-spinner-overlay d-none">
        <div class="spinner-backdrop"></div>
        <div class="professional-spinner-container">
            <!-- Main Spinner Circle -->
            <div class="spinner-ring">
                <div class="spinner-ring-inner"></div>
                <div class="spinner-dots">
                    <div class="dot dot-1"></div>
                    <div class="dot dot-2"></div>
                    <div class="dot dot-3"></div>
                    <div class="dot dot-4"></div>
                </div>
            </div>

            <!-- Logo Container -->
            <div class="spinner-logo-container">
                <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/<?php echo $_smarty_tpl->tpl_vars['template']->value;?>
/frontend/assets/img/logo/widdx-animated.svg"
                     alt="WIDDX" class="spinner-logo">
            </div>

            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-bar-container">
                    <div class="progress-bar-fill"></div>
                </div>
                <div class="progress-percentage">0%</div>
            </div>

            <!-- Animated Text -->
            <div class="spinner-text-container">
                <h4 class="spinner-title">Analyzing Your Website</h4>
                <p id="animatedText" class="spinner-subtitle typing-effect"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['we_are_analyzing_now'];?>
</p>
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>

            <!-- Floating Elements -->
            <div class="floating-elements">
                <div class="floating-element element-1"></div>
                <div class="floating-element element-2"></div>
                <div class="floating-element element-3"></div>
                <div class="floating-element element-4"></div>
            </div>
        </div>
    </div>
<?php } elseif ((isset($_smarty_tpl->tpl_vars['seoResult']->value['error']))) {?>
    <div class="alert alert-danger" role="alert">
        <i class="fas fa-exclamation-triangle mr-2"></i> <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['error'];?>

    </div>
<?php } else { ?>

    <section class="p-4 bg-white shadow-sm hover-shadow mb-3">
        <div class="container-fluid">
            <!-- Header Row -->
            <div class="row align-items-center py-3 border-bottom">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['favicon']) {?>
                            <img src="<?php echo $_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['favicon'];?>
" alt="Favicon" class="img-fluid mr-2"
                                style="height: 30px; width: 30px; object-fit: contain;">
                        <?php } else { ?>
                            <i class="fas fa-link fa-lg text-secondary mr-2"></i>
                        <?php }?>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['url']->value;?>
" class="text-success text-break"><?php echo $_smarty_tpl->tpl_vars['url']->value;?>
</a>
                    </div>
                </div>
                <div class="col-md-4 text-md-right mt-3 mt-md-0">
                    <a href="?page=seo-analyzer&action=export_pdf&url=<?php echo urlencode($_smarty_tpl->tpl_vars['url']->value);?>
" class="btn btn-primary">
                        <i class="fas fa-file-export mr-2"></i><?php echo $_smarty_tpl->tpl_vars['LANG']->value['export_to_pdf'];?>

                    </a>
                </div>
            </div>
        </div>

        <!-- Overview Section -->
        <section id="overview">
            <div class="border-0">
                <div class="row align-items-center m-4">
                    <div class="col-md-6">
                        <h2><?php echo $_smarty_tpl->tpl_vars['LANG']->value['overall_seo_score'];?>
</h2>
                        <h4><?php echo $_smarty_tpl->tpl_vars['LANG']->value['seo_score_desc'];?>
</h4>
                        <!-- Alerts -->
                        <?php if ($_smarty_tpl->tpl_vars['seoScore']->value['scorePercentage'] >= 80) {?>
                            <div class="alert alert-success mt-3" role="alert">
                                <i class="fas fa-trophy mr-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['excellent_seo'];?>

                            </div>
                        <?php } elseif ($_smarty_tpl->tpl_vars['seoScore']->value['scorePercentage'] >= 60) {?>
                            <div class="alert alert-warning mt-3" role="alert">
                                <i class="fas fa-tools mr-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['good_seo'];?>

                            </div>
                        <?php } else { ?>
                            <div class="alert alert-danger mt-3" role="alert">
                                <i class="fas fa-exclamation-triangle mr-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['poor_seo'];?>

                            </div>
                        <?php }?>
                    </div>

                    <div class="col-md-6 d-flex justify-content-center align-items-center">
                        <div class="progress-circle" data-value="<?php echo $_smarty_tpl->tpl_vars['seoScore']->value['scorePercentage'];?>
">
                            <div class="progress-circle-inner">
                                <div class="progress-circle-text"><?php echo $_smarty_tpl->tpl_vars['seoScore']->value['scorePercentage'];?>
%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Cards -->
        <div class="row g-3">
            <!-- All Items -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <div class="card h-100 shadow-sm hover-shadow">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="rounded-circle p-3 d-flex align-items-center justify-content-center"
                                    style="width: 60px; height: 60px;">
                                    <i class="fas fa-list-alt fa-2x"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-uppercase fw-bold text-muted mb-1"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['all_items'];?>
</h6>
                                <div class="fs-4 fw-bold"><?php echo $_smarty_tpl->tpl_vars['seoScore']->value['totalChecks'];?>
</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Critical Issues -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <div class="card h-100 shadow-sm hover-shadow">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-opacity-10 rounded-circle p-3 d-flex align-items-center justify-content-center"
                                    style="width: 60px; height: 60px;">
                                    <i class="fas fa-exclamation-triangle fa-2x text-danger"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-uppercase fw-bold text-muted mb-1"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['critical_issues'];?>
</h6>
                                <div class="fs-4 fw-bold text-danger"><?php echo $_smarty_tpl->tpl_vars['seoScore']->value['failedChecks'];?>
</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommended -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <div class="card h-100 shadow-sm hover-shadow">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-opacity-10 rounded-circle p-3 d-flex align-items-center justify-content-center"
                                    style="width: 60px; height: 60px;">
                                    <i class="fas fa-thumbs-up fa-2x text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-uppercase fw-bold text-muted mb-1"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['recommended'];?>
</h6>
                                <div class="fs-4 fw-bold text-warning"><?php echo $_smarty_tpl->tpl_vars['seoScore']->value['improveChecks'];?>
</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Good Results -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <div class="card h-100 shadow-sm hover-shadow">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-opacity-10 rounded-circle p-3 d-flex align-items-center justify-content-center"
                                    style="width: 60px; height: 60px;">
                                    <i class="fas fa-check-circle fa-2x text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-uppercase fw-bold text-muted mb-1"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['good_results'];?>
</h6>
                                <div class="fs-4 fw-bold text-success"><?php echo $_smarty_tpl->tpl_vars['seoScore']->value['passedChecks'];?>
</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <!-- Sidebar Navigation -->
            <div class="col-md-3">
                <div id="list-widdx-seo" class="list-group bg-white sticky-top">
                    <a class="list-group-item list-group-item-action" href="#search-preview">
                        <i class="fas fa-search me-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['search_preview'];?>

                    </a>
                    <a class="list-group-item list-group-item-action" href="#basic-seo">
                        <i class="fas fa-cogs me-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['basic_seo'];?>

                    </a>
                    <a class="list-group-item list-group-item-action" href="#advanced-seo">
                        <i class="fas fa-level-up-alt me-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['advanced_seo'];?>

                    </a>
                    <a class="list-group-item list-group-item-action" href="#performance">
                        <i class="fas fa-tachometer-alt me-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['performance'];?>

                    </a>
                    <a class="list-group-item list-group-item-action" href="#security">
                        <i class="fas fa-shield-alt me-2"></i> <?php echo $_smarty_tpl->tpl_vars['LANG']->value['security'];?>

                    </a>
                </div>
            </div>

            <!-- Content Area -->
            <div class="col-md-9">
                <div data-spy="scroll" data-target="#list-widdx-seo" data-offset="0" class="scrollspy-example">
                    <!-- Search Preview -->
                    <div id="search-preview" class="card border-0 shadow-sm mb-4">
                        <div class="card-body p-3 p-sm-4">
                            <h2 class="mb-3"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['search_preview'];?>
</h2>
                            <p class="text-muted"><?php echo $_smarty_tpl->tpl_vars['LANG']->value['search_preview_how_appears'];?>
</p>
                            <div class="google-result">
                                <div class="d-flex flex-column">
                                    <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['favicon']) {?>
                                        <div class="d-flex align-items-center mb-2">
                                            <img src="<?php echo $_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['favicon'];?>
" alt="Favicon" class="me-2 rounded"
                                                style="width: 16px; height: 16px; object-fit: cover;" />
                                            <cite class="text-muted small"><?php echo smarty_modifier_regex_replace(smarty_modifier_replace(smarty_modifier_replace($_smarty_tpl->tpl_vars['url']->value,'https://',''),'http://',''),'/\/.*$/','');?>
</cite>
                                        </div>
                                    <?php }?>
                                    <h3 class="mb-1 fs-5">
                                        <a href="<?php echo $_smarty_tpl->tpl_vars['url']->value;?>
" class="text-primary text-decoration-none fw-normal">
                                            <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['title'];?>

                                        </a>
                                    </h3>
                                    <div class="text-success small mb-2"><?php echo $_smarty_tpl->tpl_vars['url']->value;?>
</div>
                                    <div class="text-dark small lh-sm"><?php echo $_smarty_tpl->tpl_vars['seoResult']->value['SeoPreview']['description'];?>
</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Analysis -->
                    <div id="content-analysis" class="card border-0 shadow-sm mb-4">
                        <div class="card-body p-3 p-sm-4">
                            <h2 class="mb-3">Content Analysis</h2>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Word Count</h5>
                                            <p class="card-text">
                                                <span class="fs-4 fw-bold"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['wordCount'])===null||$tmp==='' ? '0' : $tmp);?>
</span>
                                                <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['wordCount'] < 300) {?>
                                                    <span class="text-danger ms-2"><i class="fas fa-exclamation-triangle"></i> Low word count</span>
                                                <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['wordCount'] < 600) {?>
                                                    <span class="text-warning ms-2"><i class="fas fa-exclamation-circle"></i> Medium word count</span>
                                                <?php } else { ?>
                                                    <span class="text-success ms-2"><i class="fas fa-check-circle"></i> Good word count</span>
                                                <?php }?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Keyword Density</h5>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>Keyword</th>
                                                            <th>Density</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['keywordDensity'], 'density', false, 'keyword');
$_smarty_tpl->tpl_vars['density']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['keyword']->value => $_smarty_tpl->tpl_vars['density']->value) {
$_smarty_tpl->tpl_vars['density']->do_else = false;
?>
                                                            <tr>
                                                                <td><?php echo $_smarty_tpl->tpl_vars['keyword']->value;?>
</td>
                                                                <td><?php echo $_smarty_tpl->tpl_vars['density']->value;?>
%</td>
                                                            </tr>
                                                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                                        <?php if (!$_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['keywordDensity']) {?>
                                                            <tr><td colspan="2" class="text-muted">No keywords found</td></tr>
                                                        <?php }?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Broken Links</h5>
                                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenLinks'] && count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenLinks']) > 0) {?>
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Link Text</th>
                                                                <th>URL</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenLinks'], 'link');
$_smarty_tpl->tpl_vars['link']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['link']->value) {
$_smarty_tpl->tpl_vars['link']->do_else = false;
?>
                                                                <tr>
                                                                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['link']->value['text'])===null||$tmp==='' ? '-' : $tmp);?>
</td>
                                                                    <td><a href="<?php echo $_smarty_tpl->tpl_vars['link']->value['url'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['link']->value['url'];?>
</a></td>
                                                                </tr>
                                                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php } else { ?>
                                                <p class="text-success"><i class="fas fa-check-circle"></i> No broken links found.</p>
                                            <?php }?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">Broken Images</h5>
                                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenImages'] && count($_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenImages']) > 0) {?>
                                                <div class="table-responsive">
                                                    <table class="table table-sm">
                                                        <thead>
                                                            <tr>
                                                                <th>Alt Text</th>
                                                                <th>URL</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['basicSeo']['brokenImages'], 'image');
$_smarty_tpl->tpl_vars['image']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['image']->value) {
$_smarty_tpl->tpl_vars['image']->do_else = false;
?>
                                                                <tr>
                                                                    <td><?php echo (($tmp = @$_smarty_tpl->tpl_vars['image']->value['alt'])===null||$tmp==='' ? '-' : $tmp);?>
</td>
                                                                    <td><a href="<?php echo $_smarty_tpl->tpl_vars['image']->value['url'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['image']->value['url'];?>
</a></td>
                                                                </tr>
                                                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            <?php } else { ?>
                                                <p class="text-success"><i class="fas fa-check-circle"></i> No broken images found.</p>
                                            <?php }?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Include Analysis Sections -->
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/pages/seo-s/widdx-basic-seo.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/pages/seo-s/widdx-advanced-seo.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/pages/seo-s/widdx-performance-seo.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/pages/seo-s/widdx-security-seo.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                </div>
            </div>
        </div>
    </section>
<?php }?>

<style>
    .google-result a {
        color: #1a0dab !important;
    }

    .google-result a:hover {
        text-decoration: underline !important;
    }

    .google-result cite {
        color: #006621;
        font-style: normal;
    }

    .google-result .text-success {
        color: #006621 !important;
    }

    .google-result .text-dark {
        color: #545454 !important;
    }

    .card {
        transition: box-shadow 0.3s ease-in-out;
    }

    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    }

    .typing-effect {
        overflow: hidden;
        /* Ensures the content is hidden until it's "typed" */
        border-right: .15em solid orange;
        /* Cursor */
        white-space: nowrap;
        /* Prevents wrapping */
        animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
    }

    @keyframes typing {
        from {
            width: 0
        }

        to {
            width: 100%
        }
    }

    @keyframes blink-caret {
        from,
        to {
            border-color: transparent
        }

        50% {
            border-color: orange;
        }
    }

    .bg-gradient-primary {
        background: linear-gradient(45deg, #007bff, #00a5ff);
    }

    .rounded-pill-left,
    .rounded-pill-right {
        border-radius: 50rem 0 0 50rem !important;
    }

    .rounded-pill-right {
        border-radius: 0 50rem 50rem 0 !important;
    }

    .btn-hover-effect:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .hover-card {
        transition: all 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    }

    /* Professional Loading Spinner Styles */
    .professional-spinner-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    .spinner-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg,
            rgba(13, 27, 62, 0.95) 0%,
            rgba(27, 38, 79, 0.9) 50%,
            rgba(65, 105, 225, 0.85) 100%);
        animation: backdropPulse 4s ease-in-out infinite;
    }

    .professional-spinner-container {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        z-index: 1;
    }

    /* Main Spinner Ring */
    .spinner-ring {
        position: relative;
        width: 120px;
        height: 120px;
        margin-bottom: 30px;
    }

    .spinner-ring-inner {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 3px solid transparent;
        border-top: 3px solid #00d4ff;
        border-right: 3px solid #7c3aed;
        border-radius: 50%;
        animation: spinRing 2s linear infinite;
    }

    .spinner-ring-inner::before {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        width: calc(100% + 6px);
        height: calc(100% + 6px);
        border: 2px solid transparent;
        border-bottom: 2px solid #ff6b6b;
        border-left: 2px solid #4ecdc4;
        border-radius: 50%;
        animation: spinRingReverse 3s linear infinite;
    }

    /* Spinner Dots */
    .spinner-dots {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .dot {
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: linear-gradient(45deg, #00d4ff, #7c3aed);
        animation: dotPulse 2s ease-in-out infinite;
    }

    .dot-1 { top: -40px; left: -4px; animation-delay: 0s; }
    .dot-2 { right: -40px; top: -4px; animation-delay: 0.5s; }
    .dot-3 { bottom: -40px; right: -4px; animation-delay: 1s; }
    .dot-4 { left: -40px; bottom: -4px; animation-delay: 1.5s; }

    /* Logo Container */
    .spinner-logo-container {
        position: relative;
        margin-bottom: 25px;
        animation: logoFloat 3s ease-in-out infinite;
    }

    .spinner-logo {
        width: 80px;
        height: auto;
        filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
        animation: logoGlow 2s ease-in-out infinite alternate;
    }

    /* Progress Indicator */
    .progress-indicator {
        margin-bottom: 25px;
        width: 200px;
    }

    .progress-bar-container {
        width: 100%;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .progress-bar-fill {
        height: 100%;
        background: linear-gradient(90deg, #00d4ff, #7c3aed, #ff6b6b);
        background-size: 200% 100%;
        border-radius: 2px;
        animation: progressFill 3s ease-in-out infinite, gradientShift 2s linear infinite;
        width: 0%;
    }

    .progress-percentage {
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }

    /* Text Container */
    .spinner-text-container {
        text-align: center;
        margin-bottom: 20px;
    }

    .spinner-title {
        color: #ffffff;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
        animation: titleGlow 3s ease-in-out infinite alternate;
    }

    .spinner-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 15px;
        text-shadow: 0 0 10px rgba(124, 58, 237, 0.4);
    }

    /* Loading Dots */
    .loading-dots {
        display: flex;
        justify-content: center;
        gap: 8px;
    }

    .loading-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: linear-gradient(45deg, #00d4ff, #7c3aed);
        animation: dotBounce 1.4s ease-in-out infinite both;
    }

    .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
    .loading-dots span:nth-child(2) { animation-delay: -0.16s; }
    .loading-dots span:nth-child(3) { animation-delay: 0s; }

    /* Floating Elements */
    .floating-elements {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        overflow: hidden;
    }

    .floating-element {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(124, 58, 237, 0.1));
        animation: floatElement 6s ease-in-out infinite;
    }

    .element-1 {
        width: 60px;
        height: 60px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }

    .element-2 {
        width: 40px;
        height: 40px;
        top: 60%;
        right: 15%;
        animation-delay: 1.5s;
    }

    .element-3 {
        width: 80px;
        height: 80px;
        bottom: 25%;
        left: 20%;
        animation-delay: 3s;
    }

    .element-4 {
        width: 50px;
        height: 50px;
        top: 30%;
        right: 25%;
        animation-delay: 4.5s;
    }

    /* Animations */
    @keyframes backdropPulse {
        0%, 100% { opacity: 0.95; }
        50% { opacity: 0.85; }
    }

    @keyframes spinRing {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes spinRingReverse {
        0% { transform: rotate(360deg); }
        100% { transform: rotate(0deg); }
    }

    @keyframes dotPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.5);
            opacity: 0.7;
        }
    }

    @keyframes logoFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    @keyframes logoGlow {
        0% { filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5)); }
        100% { filter: drop-shadow(0 0 30px rgba(124, 58, 237, 0.7)); }
    }

    @keyframes progressFill {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        100% { background-position: 200% 50%; }
    }

    @keyframes titleGlow {
        0% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.6); }
        100% { text-shadow: 0 0 30px rgba(124, 58, 237, 0.8); }
    }

    @keyframes dotBounce {
        0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
        }
        40% {
            transform: scale(1.2);
            opacity: 1;
        }
    }

    @keyframes floatElement {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
            opacity: 0.3;
        }
        50% {
            transform: translateY(-20px) rotate(180deg);
            opacity: 0.6;
        }
    }

    /* Typing Effect */
    .typing-effect {
        overflow: hidden;
        border-right: 2px solid rgba(255, 255, 255, 0.7);
        white-space: nowrap;
        animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
    }

    @keyframes typing {
        from { width: 0; }
        to { width: 100%; }
    }

    @keyframes blink-caret {
        from, to { border-color: transparent; }
        50% { border-color: rgba(255, 255, 255, 0.7); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .spinner-ring {
            width: 100px;
            height: 100px;
        }

        .spinner-logo {
            width: 60px;
        }

        .spinner-title {
            font-size: 20px;
        }

        .spinner-subtitle {
            font-size: 14px;
        }

        .progress-indicator {
            width: 150px;
        }

        .floating-element {
            display: none;
        }
    }

    .sticky-top {
        top: 20px;
    }

    .progress-circle {
        position: relative;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background-color: #e9ecef;
    }

    .progress-circle-inner {
        position: absolute;
        top: 10px;
        left: 10px;
        right: 10px;
        bottom: 10px;
        background-color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .progress-circle-text {
        font-size: 2rem;
        font-weight: bold;
    }
</style>

<?php echo '<script'; ?>
>
    $(document).ready(function() {
        // Form submission and professional loading spinner
        $('#seoForm').on('submit', function() {
            $('#loadingSpinner').removeClass('d-none');
            startProgressAnimation();
        });

        // Professional spinner progress animation
        function startProgressAnimation() {
            let progress = 0;
            const progressBar = $('.progress-bar-fill');
            const progressText = $('.progress-percentage');
            const animatedText = $('#animatedText');

            const messages = [
                '<?php echo $_smarty_tpl->tpl_vars['LANG']->value['we_are_analyzing_now'];?>
',
                'Scanning website structure...',
                'Analyzing SEO elements...',
                'Checking performance metrics...',
                'Generating comprehensive report...',
                'Finalizing analysis...'
            ];

            let messageIndex = 0;

            const progressInterval = setInterval(() => {
                progress += Math.random() * 15 + 5; // Random increment between 5-20

                if (progress > 100) {
                    progress = 100;
                    clearInterval(progressInterval);
                }

                progressBar.css('width', progress + '%');
                progressText.text(Math.round(progress) + '%');

                // Change message every 20% progress
                const newMessageIndex = Math.floor(progress / 20);
                if (newMessageIndex !== messageIndex && newMessageIndex < messages.length) {
                    messageIndex = newMessageIndex;
                    animatedText.text(messages[messageIndex]);
                }
            }, 800);
        }

        // Scrollspy initialization
        $('body').scrollspy({
            target: '#list-widdx-seo',
            offset: 200
        });

        // Smooth scrolling for sidebar links
        $('.list-group-item').on('click', function(e) {
            e.preventDefault();
            var target = $(this).attr('href');
            $('html, body').animate({
                scrollTop: $(target).offset().top - 100
            }, 500);
        });

        // Circular progress bar
        $('.progress-circle').each(function() {
            var value = $(this).data('value');
            var color = value >= 80 ? '#28a745' : (value >= 60 ? '#ffc107' : '#dc3545');

            new ProgressBar.Circle(this, {
                color: color,
                trailColor: '#e9ecef',
                strokeWidth: 10,
                trailWidth: 10,
                duration: 1400,
                easing: 'easeInOut'
            }).animate(value / 100);
        });

        // Activate tooltip
        $('[data-toggle="tooltip"]').tooltip();

        // Add animations to sections when they come into view
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry) => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                }
            });
        });

        // Observe all sections with the class 'animate-on-scroll'
        document.querySelectorAll('.animate-on-scroll').forEach((section) => {
            observer.observe(section);
        });
    });
<?php echo '</script'; ?>
><?php }
}
