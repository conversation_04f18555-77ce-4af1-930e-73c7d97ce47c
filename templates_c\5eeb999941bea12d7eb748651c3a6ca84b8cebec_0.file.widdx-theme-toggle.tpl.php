<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:16
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\widget\widdx-theme-toggle.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c8814ef57_67978010',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '5eeb999941bea12d7eb748651c3a6ca84b8cebec' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\widget\\widdx-theme-toggle.tpl',
      1 => 1747863179,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c8814ef57_67978010 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Theme Toggle Button for Navbar -->
<li class="nav-item">
  <a class="nav-link nav-btn ww-theme-toggle" href="javascript:void(0)" aria-label="Toggle Dark/Light Mode" id="theme-toggle-btn">
    <div class="nav-icon-circle">
      <div class="ww-theme-light" data-title="Dark">
        <i class="fas fa-moon"></i>
      </div>
      <div class="ww-theme-dark" data-title="Light">
        <i class="fas fa-sun"></i>
      </div>
    </div>
  </a>
</li>
<?php }
}
