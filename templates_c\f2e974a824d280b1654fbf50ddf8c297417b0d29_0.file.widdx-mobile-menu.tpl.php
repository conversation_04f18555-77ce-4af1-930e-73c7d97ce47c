<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:17
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\partial\widdx-mobile-menu.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c89283717_54385993',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'f2e974a824d280b1654fbf50ddf8c297417b0d29' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\partial\\widdx-mobile-menu.tpl',
      1 => 1726724020,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c89283717_54385993 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Mobile Bottom Navigation -->
<nav class="mobile-bottom-nav d-md-none">
    <div class="container">
        <div class="row justify-content-around">
            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/clientarea.php" class="col mobile-nav-item text-center">
                <i class="fas fa-home d-block mb-1"></i>
                <small><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'home'),$_smarty_tpl ) );?>
</small>
            </a>
            <a href="#" class="col mobile-nav-item text-center" data-bs-toggle="modal" data-bs-target="#quickActionsModal">
                <i class="fas fa-layer-group d-block mb-1"></i>
                <small><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'actions'),$_smarty_tpl ) );?>
</small>
            </a>
            <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/cart.php?a=view" class="col mobile-nav-item text-center">
                <i class="far fa-shopping-cart d-block mb-1"></i>
                <small><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'cart'),$_smarty_tpl ) );?>
</small>
                <span id="cartItemCount" class="badge bg-primary rounded-pill position-absolute top-0 start-80 translate-middle">
                    <?php echo $_smarty_tpl->tpl_vars['cartitemcount']->value;?>

                </span>
            </a>
            <a href="#" class="col mobile-nav-item text-center" id="notifDropdown" data-bs-toggle="modal" data-bs-target="#notifModal">
                <i class="fas fa-bell d-block mb-1"></i>
                <small><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'notifications'),$_smarty_tpl ) );?>
</small>
                <span class="badge bg-danger rounded-pill position-absolute top-0 start-80 translate-middle">
                    <?php echo (($tmp = @count($_smarty_tpl->tpl_vars['clientAlerts']->value))===null||$tmp==='' ? 0 : $tmp);?>

                </span>
            </a>
        </div>
    </div>
</nav>

<!-- Notification Modal -->
<div class="modal fade" id="notifModal" tabindex="-1" aria-labelledby="notifModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <div class="modal-content border-0 shadow">
            <div class="modal-header border-0 bg-light">
                <h5 class="modal-title" id="notifModalLabel">
                    <i class="fas fa-bell me-2 text-primary"></i>
                    <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'notifications'),$_smarty_tpl ) );?>
 <span class="badge bg-primary rounded-pill ms-2"><?php echo (($tmp = @count($_smarty_tpl->tpl_vars['clientAlerts']->value))===null||$tmp==='' ? 0 : $tmp);?>
</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <ul class="list-group list-group-flush">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['clientAlerts']->value, 'alert');
$_smarty_tpl->tpl_vars['alert']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['alert']->value) {
$_smarty_tpl->tpl_vars['alert']->do_else = false;
?>
                        <li class="list-group-item border-0 py-3">
                            <a href="<?php echo $_smarty_tpl->tpl_vars['alert']->value->getLink();?>
" class="text-decoration-none text-dark">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0 me-3">
                                        <i class="fas fa-<?php if ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'danger') {?>exclamation-circle text-danger
                                                      <?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'warning') {?>exclamation-triangle text-warning
                                                      <?php } elseif ($_smarty_tpl->tpl_vars['alert']->value->getSeverity() == 'info') {?>info-circle text-info
                                                      <?php } else { ?>check-circle text-success<?php }?> fa-lg"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <?php echo $_smarty_tpl->tpl_vars['alert']->value->getMessage();?>

                                    </div>
                                    <div class="flex-shrink-0 ms-3">
                                        <i class="fas fa-chevron-right text-muted"></i>
                                    </div>
                                </div>
                            </a>
                        </li>
                    <?php
}
if ($_smarty_tpl->tpl_vars['alert']->do_else) {
?>
                        <li class="list-group-item border-0 text-center py-5">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <p class="text-muted"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'notificationsnone'),$_smarty_tpl ) );?>
</p>
                        </li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
            <div class="modal-footer border-0 bg-light">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'close'),$_smarty_tpl ) );?>
</button>
            </div>
        </div>
    </div>
</div>

<style>

</style>

<?php echo '<script'; ?>
>
jQuery(document).ready(function($) {
    $('.mobile-nav-item').on('click', function() {
        $('.mobile-nav-item').removeClass('active');
        $(this).addClass('active');
    });
});
<?php echo '</script'; ?>
><?php }
}
