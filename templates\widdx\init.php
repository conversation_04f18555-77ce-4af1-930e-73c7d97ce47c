<?php
/**
 * WIDDX Template Initialization
 * Enhanced initialization with security and performance optimizations
 */

// Prevent direct access
if (!defined('WHMCS')) {
    die('Access denied');
}

// Define template constants
define('WIDDX_TEMPLATE_PATH', __DIR__);
define('WIDDX_VERSION', '2.0.0-secure');
define('WIDDX_DEBUG', defined('WHMCS_DEBUG') && WHMCS_DEBUG);

// Include core classes
require_once WIDDX_TEMPLATE_PATH . '/helpers/WiddxErrorHandler.php';
require_once WIDDX_TEMPLATE_PATH . '/helpers/WiddxSessionManager.php';
require_once WIDDX_TEMPLATE_PATH . '/config/security.php';
require_once WIDDX_TEMPLATE_PATH . '/config/performance.php';

/**
 * WIDDX Template Manager
 */
class WiddxTemplate {
    
    private static $instance = null;
    private $config = [];
    private $assets = [];
    private $sessionManager;
    
    private function __construct() {
        $this->sessionManager = WiddxSessionManager::getInstance();
        $this->loadConfiguration();
        $this->initializeAssets();
        $this->setupErrorReporting();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Load template configuration
     */
    private function loadConfiguration() {
        $this->config = [
            'template_name' => 'WIDDX',
            'version' => WIDDX_VERSION,
            'debug' => WIDDX_DEBUG,
            'cache_enabled' => !WIDDX_DEBUG,
            'minify_assets' => !WIDDX_DEBUG,
            'security_headers' => true,
            'performance_optimization' => true,
            'rtl_support' => true,
            'dark_mode' => true,
            'supported_languages' => ['english', 'arabic', 'hebrew', 'farsi'],
            'critical_css' => [
                'frontend/assets/css/variables.css',
                'frontend/assets/css/widdx-optimized.css'
            ],
            'critical_js' => [
                'frontend/assets/js/widdx-main.js'
            ]
        ];
        
        // Load custom configuration if exists
        $customConfigFile = WIDDX_TEMPLATE_PATH . '/config/custom.php';
        if (file_exists($customConfigFile)) {
            $customConfig = include $customConfigFile;
            if (is_array($customConfig)) {
                $this->config = array_merge($this->config, $customConfig);
            }
        }
    }
    
    /**
     * Initialize asset management
     */
    private function initializeAssets() {
        $this->assets = [
            'css' => [
                'critical' => [],
                'deferred' => []
            ],
            'js' => [
                'critical' => [],
                'deferred' => []
            ],
            'preload' => [],
            'prefetch' => []
        ];
        
        // Add critical CSS
        foreach ($this->config['critical_css'] as $css) {
            $this->addAsset('css', $css, true);
        }
        
        // Add critical JS
        foreach ($this->config['critical_js'] as $js) {
            $this->addAsset('js', $js, true);
        }
    }
    
    /**
     * Setup error reporting based on environment
     */
    private function setupErrorReporting() {
        if (WIDDX_DEBUG) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
            ini_set('log_errors', 1);
        } else {
            error_reporting(E_ERROR | E_WARNING | E_PARSE);
            ini_set('display_errors', 0);
            ini_set('log_errors', 1);
        }
    }
    
    /**
     * Add asset to the queue
     */
    public function addAsset($type, $path, $critical = false) {
        $fullPath = WIDDX_TEMPLATE_PATH . '/' . ltrim($path, '/');
        
        if (file_exists($fullPath)) {
            $asset = [
                'path' => $path,
                'full_path' => $fullPath,
                'version' => filemtime($fullPath),
                'size' => filesize($fullPath)
            ];
            
            if ($critical) {
                $this->assets[$type]['critical'][] = $asset;
            } else {
                $this->assets[$type]['deferred'][] = $asset;
            }
            
            return true;
        }
        
        WiddxErrorHandler::logCustomError("Asset not found: {$fullPath}");
        return false;
    }
    
    /**
     * Get asset URL with versioning
     */
    public function getAssetUrl($path) {
        $fullPath = WIDDX_TEMPLATE_PATH . '/' . ltrim($path, '/');
        $version = file_exists($fullPath) ? filemtime($fullPath) : time();
        
        return "/templates/widdx/{$path}?v={$version}";
    }
    
    /**
     * Generate critical CSS
     */
    public function getCriticalCSS() {
        $criticalCSS = '';
        
        foreach ($this->assets['css']['critical'] as $asset) {
            if (file_exists($asset['full_path'])) {
                $css = file_get_contents($asset['full_path']);
                
                if ($this->config['minify_assets']) {
                    $css = WiddxPerformance::minifyCSS($css);
                }
                
                $criticalCSS .= $css . "\n";
            }
        }
        
        return $criticalCSS;
    }
    
    /**
     * Generate CSS links
     */
    public function getCSSLinks($critical = false) {
        $links = '';
        $assets = $critical ? $this->assets['css']['critical'] : $this->assets['css']['deferred'];
        
        foreach ($assets as $asset) {
            $url = $this->getAssetUrl($asset['path']);
            $links .= "<link rel=\"stylesheet\" href=\"{$url}\">\n";
        }
        
        return $links;
    }
    
    /**
     * Generate JS scripts
     */
    public function getJSScripts($critical = false) {
        $scripts = '';
        $assets = $critical ? $this->assets['js']['critical'] : $this->assets['js']['deferred'];
        
        foreach ($assets as $asset) {
            $url = $this->getAssetUrl($asset['path']);
            $defer = $critical ? '' : ' defer';
            $scripts .= "<script src=\"{$url}\"{$defer}></script>\n";
        }
        
        return $scripts;
    }
    
    /**
     * Check if RTL language
     */
    public function isRTL($language = null) {
        if ($language === null) {
            $language = $_SESSION['Language'] ?? 'english';
        }
        
        $rtlLanguages = ['arabic', 'hebrew', 'farsi'];
        return in_array(strtolower($language), $rtlLanguages);
    }
    
    /**
     * Get language direction
     */
    public function getLanguageDirection($language = null) {
        return $this->isRTL($language) ? 'rtl' : 'ltr';
    }
    
    /**
     * Get template configuration
     */
    public function getConfig($key = null) {
        if ($key === null) {
            return $this->config;
        }
        
        return $this->config[$key] ?? null;
    }
    
    /**
     * Set template configuration
     */
    public function setConfig($key, $value) {
        $this->config[$key] = $value;
    }
    
    /**
     * Generate meta tags
     */
    public function getMetaTags() {
        $meta = '';
        
        // Basic meta tags
        $meta .= "<meta charset=\"UTF-8\">\n";
        $meta .= "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n";
        $meta .= "<meta name=\"generator\" content=\"WIDDX Template v{$this->config['version']}\">\n";
        
        // Security meta tags
        if ($this->config['security_headers']) {
            $meta .= "<meta http-equiv=\"X-Content-Type-Options\" content=\"nosniff\">\n";
            $meta .= "<meta http-equiv=\"X-Frame-Options\" content=\"DENY\">\n";
            $meta .= "<meta http-equiv=\"X-XSS-Protection\" content=\"1; mode=block\">\n";
        }
        
        // Performance meta tags
        if ($this->config['performance_optimization']) {
            $meta .= "<meta http-equiv=\"Cache-Control\" content=\"public, max-age=300\">\n";
        }
        
        return $meta;
    }
    
    /**
     * Generate preload links
     */
    public function getPreloadLinks() {
        $links = '';
        
        // Preload critical fonts
        $fonts = [
            '/templates/widdx/frontend/assets/fonts/cairo/cairo-regular.woff2',
            '/templates/widdx/frontend/assets/fonts/cairo/cairo-bold.woff2'
        ];
        
        foreach ($fonts as $font) {
            if (file_exists(WIDDX_TEMPLATE_PATH . $font)) {
                $links .= "<link rel=\"preload\" href=\"{$font}\" as=\"font\" type=\"font/woff2\" crossorigin>\n";
            }
        }
        
        // Preload critical CSS
        foreach ($this->assets['css']['critical'] as $asset) {
            $url = $this->getAssetUrl($asset['path']);
            $links .= "<link rel=\"preload\" href=\"{$url}\" as=\"style\">\n";
        }
        
        return $links;
    }
    
    /**
     * Generate resource hints
     */
    public function getResourceHints() {
        $hints = '';
        
        // DNS prefetch for external resources
        $externalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'cdn.jsdelivr.net',
            'cdnjs.cloudflare.com'
        ];
        
        foreach ($externalDomains as $domain) {
            $hints .= "<link rel=\"dns-prefetch\" href=\"//{$domain}\">\n";
        }
        
        return $hints;
    }
    
    /**
     * Get CSRF token for forms
     */
    public function getCSRFToken() {
        return $this->sessionManager->getCSRFToken();
    }
    
    /**
     * Generate CSRF input field
     */
    public function getCSRFInput() {
        $token = $this->getCSRFToken();
        return "<input type=\"hidden\" name=\"csrf_token\" value=\"{$token}\">";
    }
    
    /**
     * Validate CSRF token
     */
    public function validateCSRF($token) {
        return $this->sessionManager->validateCSRFToken($token);
    }
    
    /**
     * Get flash messages
     */
    public function getFlashMessages() {
        return $this->sessionManager->getAllFlashMessages();
    }
    
    /**
     * Set flash message
     */
    public function setFlashMessage($type, $message) {
        return $this->sessionManager->setFlashMessage($type, $message);
    }
    
    /**
     * Get template statistics
     */
    public function getStats() {
        return [
            'template_name' => $this->config['template_name'],
            'version' => $this->config['version'],
            'debug_mode' => $this->config['debug'],
            'cache_enabled' => $this->config['cache_enabled'],
            'assets_loaded' => [
                'critical_css' => count($this->assets['css']['critical']),
                'deferred_css' => count($this->assets['css']['deferred']),
                'critical_js' => count($this->assets['js']['critical']),
                'deferred_js' => count($this->assets['js']['deferred'])
            ],
            'session_active' => $this->sessionManager->isSessionActive(),
            'cache_stats' => WiddxPerformance::getCacheStats()
        ];
    }
    
    /**
     * Clean up resources
     */
    public function cleanup() {
        // Clean expired cache
        WiddxPerformance::cleanExpiredCache();
        
        // Log template usage
        if (!WIDDX_DEBUG) {
            WiddxErrorHandler::logCustomError('Template cleanup completed', [
                'memory_usage' => memory_get_peak_usage(true),
                'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ]);
        }
    }
}

// Initialize template
$widdxTemplate = WiddxTemplate::getInstance();

// Register shutdown function for cleanup
register_shutdown_function(function() use ($widdxTemplate) {
    $widdxTemplate->cleanup();
});

// Make template instance globally available
$GLOBALS['widdxTemplate'] = $widdxTemplate;