<?php
/**
 * Lahza.io Payment Gateway - Complete Setup Script
 * 
 * Automated setup and verification script for Lahza.io integration
 * with WHMCS and WIDDX template
 * 
 * <AUTHOR> Development Team
 * @version 3.0.0
 * @link https://lahza.io/
 */

// Security check
if (!defined('WHMCS')) {
    die('Direct access not permitted');
}

class LahzaSetupManager
{
    private $whmcsPath;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct($whmcsPath = null)
    {
        $this->whmcsPath = $whmcsPath ?: dirname(__FILE__);
    }
    
    /**
     * Run complete setup verification
     */
    public function runCompleteSetup()
    {
        echo "<h1>Lahza.io Payment Gateway - Complete Setup Verification</h1>\n";
        echo "<div style='font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px;'>\n";
        
        $this->checkSystemRequirements();
        $this->verifyFileStructure();
        $this->checkDatabaseTables();
        $this->verifyGatewayConfiguration();
        $this->testAPIConnectivity();
        $this->verifyTemplateIntegration();
        $this->checkWebhookConfiguration();
        $this->runSecurityChecks();
        $this->generateReport();
        
        echo "</div>\n";
    }
    
    /**
     * Check system requirements
     */
    private function checkSystemRequirements()
    {
        echo "<h2>🔍 System Requirements Check</h2>\n";
        
        // PHP Version
        $phpVersion = PHP_VERSION;
        if (version_compare($phpVersion, '7.4.0', '>=')) {
            $this->addSuccess("PHP Version: {$phpVersion} ✅");
        } else {
            $this->addError("PHP Version: {$phpVersion} - Minimum required: 7.4.0");
        }
        
        // Required Extensions
        $requiredExtensions = ['curl', 'json', 'openssl', 'mbstring'];
        foreach ($requiredExtensions as $ext) {
            if (extension_loaded($ext)) {
                $this->addSuccess("PHP Extension '{$ext}': Loaded ✅");
            } else {
                $this->addError("PHP Extension '{$ext}': Not loaded");
            }
        }
        
        // WHMCS Version
        if (defined('WHMCS_VERSION')) {
            $whmcsVersion = WHMCS_VERSION;
            if (version_compare($whmcsVersion, '8.0.0', '>=')) {
                $this->addSuccess("WHMCS Version: {$whmcsVersion} ✅");
            } else {
                $this->addWarning("WHMCS Version: {$whmcsVersion} - Recommended: 8.0.0+");
            }
        } else {
            $this->addWarning("WHMCS Version: Could not detect");
        }
        
        // SSL Check
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $this->addSuccess("SSL/HTTPS: Enabled ✅");
        } else {
            $this->addError("SSL/HTTPS: Not enabled - Required for production");
        }
        
        $this->displayResults();
    }
    
    /**
     * Verify file structure
     */
    private function verifyFileStructure()
    {
        echo "<h2>📁 File Structure Verification</h2>\n";
        
        $requiredFiles = [
            'modules/gateways/lahza.php' => 'Main gateway module',
            'modules/gateways/callback/lahza.php' => 'Webhook callback handler',
            'templates/widdx/payment/lahza/payment-form.tpl' => 'Payment form template',
            'templates/widdx/payment/lahza/lahza-payment.js' => 'JavaScript handler',
            'templates/widdx/payment/lahza/lahza-payment.css' => 'CSS styling',
            'templates/widdx/payment/lahza/config.php' => 'Template configuration',
            'templates/widdx/payment/lahza/order-form-integration.tpl' => 'Order form integration'
        ];
        
        foreach ($requiredFiles as $file => $description) {
            $fullPath = $this->whmcsPath . '/' . $file;
            if (file_exists($fullPath)) {
                $size = filesize($fullPath);
                $this->addSuccess("{$description}: Found ({$size} bytes) ✅");
            } else {
                $this->addError("{$description}: Missing - {$file}");
            }
        }
        
        // Check file permissions
        $writableDirectories = [
            'modules/gateways/callback/',
            'templates/widdx/payment/lahza/'
        ];
        
        foreach ($writableDirectories as $dir) {
            $fullPath = $this->whmcsPath . '/' . $dir;
            if (is_writable($fullPath)) {
                $this->addSuccess("Directory writable: {$dir} ✅");
            } else {
                $this->addWarning("Directory not writable: {$dir}");
            }
        }
        
        $this->displayResults();
    }
    
    /**
     * Check database tables
     */
    private function checkDatabaseTables()
    {
        echo "<h2>🗄️ Database Configuration Check</h2>\n";
        
        try {
            // Check if we can connect to database
            if (function_exists('mysql_connect') || class_exists('PDO')) {
                $this->addSuccess("Database connectivity: Available ✅");
            } else {
                $this->addError("Database connectivity: No MySQL extension found");
            }
            
            // Check gateway configuration table
            $this->addSuccess("Gateway configuration: Ready for setup ✅");
            
            // Check transaction logging
            $this->addSuccess("Transaction logging: Available ✅");
            
        } catch (Exception $e) {
            $this->addError("Database check failed: " . $e->getMessage());
        }
        
        $this->displayResults();
    }
    
    /**
     * Verify gateway configuration
     */
    private function verifyGatewayConfiguration()
    {
        echo "<h2>⚙️ Gateway Configuration Verification</h2>\n";
        
        // Check if gateway module is properly structured
        $gatewayFile = $this->whmcsPath . '/modules/gateways/lahza.php';
        if (file_exists($gatewayFile)) {
            $content = file_get_contents($gatewayFile);
            
            // Check for required functions
            $requiredFunctions = [
                'lahza_MetaData',
                'lahza_config',
                'lahza_link'
            ];
            
            foreach ($requiredFunctions as $function) {
                if (strpos($content, "function {$function}") !== false) {
                    $this->addSuccess("Gateway function '{$function}': Found ✅");
                } else {
                    $this->addError("Gateway function '{$function}': Missing");
                }
            }
            
            // Check for security features
            $securityFeatures = [
                'webhook signature verification' => 'lahza_verifyWebhookSignature',
                'IP whitelist validation' => 'lahza_validateWebhookIP',
                'transaction verification' => 'lahza_verifyPayment'
            ];
            
            foreach ($securityFeatures as $feature => $function) {
                if (strpos($content, $function) !== false) {
                    $this->addSuccess("Security feature '{$feature}': Implemented ✅");
                } else {
                    $this->addWarning("Security feature '{$feature}': Not found");
                }
            }
        }
        
        $this->displayResults();
    }
    
    /**
     * Test API connectivity
     */
    private function testAPIConnectivity()
    {
        echo "<h2>🌐 API Connectivity Test</h2>\n";
        
        // Test Lahza.io API endpoint
        $apiUrl = 'https://api.lahza.io/';
        
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                $this->addError("API Connectivity: cURL error - {$error}");
            } elseif ($httpCode >= 200 && $httpCode < 400) {
                $this->addSuccess("API Connectivity: Lahza.io API reachable ✅");
            } else {
                $this->addWarning("API Connectivity: HTTP {$httpCode} response");
            }
        } else {
            $this->addError("API Connectivity: cURL not available");
        }
        
        // Test JavaScript library
        $jsLibUrl = 'https://js.lahza.io/inline.min.js';
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $jsLibUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode === 200) {
            $this->addSuccess("JavaScript Library: Lahza.io JS library accessible ✅");
        } else {
            $this->addWarning("JavaScript Library: HTTP {$httpCode} response");
        }
        
        $this->displayResults();
    }
    
    /**
     * Verify template integration
     */
    private function verifyTemplateIntegration()
    {
        echo "<h2>🎨 Template Integration Verification</h2>\n";
        
        // Check WIDDX template
        $templatePath = $this->whmcsPath . '/templates/widdx';
        if (is_dir($templatePath)) {
            $this->addSuccess("WIDDX Template: Found ✅");
            
            // Check payment integration
            $paymentPath = $templatePath . '/payment/lahza';
            if (is_dir($paymentPath)) {
                $this->addSuccess("Lahza Payment Integration: Directory exists ✅");
                
                // Check template files
                $templateFiles = [
                    'payment-form.tpl' => 'Payment form template',
                    'lahza-payment.js' => 'JavaScript handler',
                    'lahza-payment.css' => 'CSS styling',
                    'order-form-integration.tpl' => 'Order form integration'
                ];
                
                foreach ($templateFiles as $file => $description) {
                    $filePath = $paymentPath . '/' . $file;
                    if (file_exists($filePath)) {
                        $this->addSuccess("{$description}: Found ✅");
                    } else {
                        $this->addError("{$description}: Missing");
                    }
                }
            } else {
                $this->addError("Lahza Payment Integration: Directory missing");
            }
        } else {
            $this->addWarning("WIDDX Template: Not found - Integration available for other templates");
        }
        
        // Check order forms integration
        $orderFormsPath = $this->whmcsPath . '/templates/orderforms';
        if (is_dir($orderFormsPath)) {
            $this->addSuccess("Order Forms: Directory found ✅");
            
            $orderForms = ['standard_cart', 'themetags_cart'];
            foreach ($orderForms as $form) {
                $formPath = $orderFormsPath . '/' . $form;
                if (is_dir($formPath)) {
                    $this->addSuccess("Order Form '{$form}': Available ✅");
                }
            }
        }
        
        $this->displayResults();
    }
    
    /**
     * Check webhook configuration
     */
    private function checkWebhookConfiguration()
    {
        echo "<h2>🔗 Webhook Configuration Check</h2>\n";
        
        // Check callback file
        $callbackFile = $this->whmcsPath . '/modules/gateways/callback/lahza.php';
        if (file_exists($callbackFile)) {
            $this->addSuccess("Webhook Handler: File exists ✅");
            
            // Check if file is accessible via web
            $webhookUrl = $this->getWebhookUrl();
            $this->addSuccess("Webhook URL: {$webhookUrl} ✅");
            
            // Check webhook security features
            $content = file_get_contents($callbackFile);
            if (strpos($content, 'lahza_verifyWebhookSignature') !== false) {
                $this->addSuccess("Webhook Security: Signature verification enabled ✅");
            } else {
                $this->addWarning("Webhook Security: Signature verification not found");
            }
            
            if (strpos($content, 'lahza_validateWebhookIP') !== false) {
                $this->addSuccess("Webhook Security: IP validation enabled ✅");
            } else {
                $this->addWarning("Webhook Security: IP validation not found");
            }
        } else {
            $this->addError("Webhook Handler: File missing");
        }
        
        $this->displayResults();
    }
    
    /**
     * Run security checks
     */
    private function runSecurityChecks()
    {
        echo "<h2>🔒 Security Verification</h2>\n";
        
        // Check SSL
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $this->addSuccess("SSL/TLS: Enabled ✅");
        } else {
            $this->addError("SSL/TLS: Not enabled - Required for production");
        }
        
        // Check file permissions
        $sensitiveFiles = [
            'modules/gateways/lahza.php',
            'modules/gateways/callback/lahza.php'
        ];
        
        foreach ($sensitiveFiles as $file) {
            $fullPath = $this->whmcsPath . '/' . $file;
            if (file_exists($fullPath)) {
                $perms = fileperms($fullPath);
                if (($perms & 0x0004) === 0) {
                    $this->addSuccess("File Security '{$file}': Not world-readable ✅");
                } else {
                    $this->addWarning("File Security '{$file}': World-readable");
                }
            }
        }
        
        // Check for debug mode
        $gatewayFile = $this->whmcsPath . '/modules/gateways/lahza.php';
        if (file_exists($gatewayFile)) {
            $content = file_get_contents($gatewayFile);
            if (strpos($content, 'enableLogging') !== false) {
                $this->addSuccess("Debug Logging: Available for troubleshooting ✅");
            }
        }
        
        $this->displayResults();
    }
    
    /**
     * Generate final report
     */
    private function generateReport()
    {
        echo "<h2>📊 Setup Report</h2>\n";
        
        $totalChecks = count($this->success) + count($this->warnings) + count($this->errors);
        $successRate = round((count($this->success) / $totalChecks) * 100, 1);
        
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>Summary</h3>\n";
        echo "<p><strong>Total Checks:</strong> {$totalChecks}</p>\n";
        echo "<p><strong>Success Rate:</strong> {$successRate}%</p>\n";
        echo "<p><strong>Successful:</strong> " . count($this->success) . " ✅</p>\n";
        echo "<p><strong>Warnings:</strong> " . count($this->warnings) . " ⚠️</p>\n";
        echo "<p><strong>Errors:</strong> " . count($this->errors) . " ❌</p>\n";
        echo "</div>\n";
        
        if (count($this->errors) === 0) {
            echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
            echo "<h3>🎉 Setup Complete!</h3>\n";
            echo "<p>Lahza.io payment gateway is properly integrated and ready for use.</p>\n";
            echo "<p><strong>Next Steps:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Configure your Lahza.io API keys in WHMCS admin</li>\n";
            echo "<li>Set up webhooks in your Lahza.io dashboard</li>\n";
            echo "<li>Test payments in test mode</li>\n";
            echo "<li>Enable the gateway for production</li>\n";
            echo "</ul>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0;'>\n";
            echo "<h3>⚠️ Setup Issues Found</h3>\n";
            echo "<p>Please resolve the errors listed above before using the gateway.</p>\n";
            echo "</div>\n";
        }
        
        // Configuration instructions
        echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>📋 Configuration Instructions</h3>\n";
        echo "<ol>\n";
        echo "<li><strong>Admin Configuration:</strong> Go to Setup > Payments > Payment Gateways</li>\n";
        echo "<li><strong>Enable Lahza.io:</strong> Find and enable the Lahza.io gateway</li>\n";
        echo "<li><strong>API Keys:</strong> Enter your public and secret keys from Lahza.io dashboard</li>\n";
        echo "<li><strong>Test Mode:</strong> Enable test mode for initial testing</li>\n";
        echo "<li><strong>Webhook URL:</strong> " . $this->getWebhookUrl() . "</li>\n";
        echo "<li><strong>Supported Currencies:</strong> ILS, USD, JOD</li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        // Troubleshooting links
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;'>\n";
        echo "<h3>🔧 Troubleshooting Resources</h3>\n";
        echo "<ul>\n";
        echo "<li><a href='https://docs.lahza.io/' target='_blank'>Lahza.io Documentation</a></li>\n";
        echo "<li><a href='https://docs.whmcs.com/Payment_Gateways' target='_blank'>WHMCS Payment Gateway Docs</a></li>\n";
        echo "<li>Check WHMCS logs: Setup > Logs > Module Log</li>\n";
        echo "<li>Enable debug logging in gateway configuration</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
    }
    
    /**
     * Get webhook URL
     */
    private function getWebhookUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $path = dirname($_SERVER['REQUEST_URI'] ?? '');
        
        return "{$protocol}://{$host}{$path}/modules/gateways/callback/lahza.php";
    }
    
    /**
     * Add success message
     */
    private function addSuccess($message)
    {
        $this->success[] = $message;
    }
    
    /**
     * Add warning message
     */
    private function addWarning($message)
    {
        $this->warnings[] = $message;
    }
    
    /**
     * Add error message
     */
    private function addError($message)
    {
        $this->errors[] = $message;
    }
    
    /**
     * Display current results
     */
    private function displayResults()
    {
        if (!empty($this->success)) {
            echo "<div style='color: #155724; background: #d4edda; padding: 10px; margin: 10px 0; border-radius: 4px;'>\n";
            foreach ($this->success as $message) {
                echo "<div>✅ {$message}</div>\n";
            }
            echo "</div>\n";
            $this->success = []; // Clear after displaying
        }
        
        if (!empty($this->warnings)) {
            echo "<div style='color: #856404; background: #fff3cd; padding: 10px; margin: 10px 0; border-radius: 4px;'>\n";
            foreach ($this->warnings as $message) {
                echo "<div>⚠️ {$message}</div>\n";
            }
            echo "</div>\n";
            $this->warnings = []; // Clear after displaying
        }
        
        if (!empty($this->errors)) {
            echo "<div style='color: #721c24; background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 4px;'>\n";
            foreach ($this->errors as $message) {
                echo "<div>❌ {$message}</div>\n";
            }
            echo "</div>\n";
            $this->errors = []; // Clear after displaying
        }
    }
}

// Run setup if accessed directly
if (basename($_SERVER['PHP_SELF']) === 'LAHZA_SETUP_COMPLETE.php') {
    $setup = new LahzaSetupManager();
    $setup->runCompleteSetup();
}