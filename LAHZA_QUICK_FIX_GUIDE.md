# دليل الإصلاح السريع لمشكلة Lahza.io Webhook

## 🚨 المشكلة: تم الدفع بنجاح ولكن لم يتم تحديث حالة الفاتورة

### السبب الأكثر شيوعاً:
عدم وصول إشعار الـ webhook من Lahza.io إلى WHMCS بسبب:
1. عدم تكوين الـ webhook في لوحة تحكم Lahza.io
2. مشكلة في الوصول لملف الـ callback
3. خطأ في التحقق من التوقيع
4. مشكلة في إعدادات الخادم

---

## 🔧 الحل السريع (5 دقائق)

### الخطوة 1: تشخيص المشكلة
```
1. افتح: https://yourdomain.com/lahza_webhook_debug.php?debug=lahza2024
2. راجع النتائج وحدد المشكلة
```

### الخطوة 2: إصلاح المشكلة
```
1. افتح: https://yourdomain.com/lahza_webhook_fix.php?fix=lahza2024
2. استخدم أدوات الإصلاح المناسبة
```

### الخطوة 3: معالجة المدفوعات المعلقة
```
1. افتح: https://yourdomain.com/lahza_manual_payment.php?process=lahza2024
2. أدخل بيانات المعاملة من Lahza.io
3. اضغط "معالجة الدفع"
```

---

## 🎯 الحلول الشائعة

### 1. إعداد Webhook في Lahza.io Dashboard

**أ. سجل دخول إلى لوحة تحكم Lahza.io**

**ب. اذهب إلى Settings → Webhooks**

**ج. أضف webhook جديد:**
```
URL: https://yourdomain.com/modules/gateways/callback/lahza.php
Events: charge.success, refund.processed
Status: Active
```

### 2. تفعيل تسجيل الأحداث في WHMCS

**أ. اذهب إلى WHMCS Admin → Setup → Payments → Payment Gateways**

**ب. ابحث عن Lahza.io واضغط Manage**

**ج. فعّل "Enable Logging"**

### 3. فحص إعدادات الخادم

**أ. تأكد من تفعيل SSL/HTTPS**

**ب. تحقق من أن الخادم يقبل POST requests**

**ج. تأكد من عدم حجب IP addresses الخاصة بـ Lahza.io:**
```
*************
**************
```

### 4. اختبار الـ Webhook يدوياً

```bash
curl -X POST https://yourdomain.com/modules/gateways/callback/lahza.php \
  -H "Content-Type: application/json" \
  -H "X-Lahza-Signature: sha256=test" \
  -d '{
    "event": "charge.success",
    "data": {
      "reference": "WHMCS_123_test",
      "status": "success",
      "amount": 10000,
      "currency": "USD",
      "metadata": {
        "invoice_id": "123"
      }
    }
  }'
```

---

## 🔍 فحص السجلات

### 1. سجلات WHMCS
```
Admin → Setup → Logs → Module Log
Admin → Setup → Logs → Gateway Log
Admin → Setup → Logs → Activity Log
```

### 2. سجلات الخادم
```
/var/log/apache2/error.log
/var/log/nginx/error.log
/var/log/php_errors.log
```

### 3. سجلات Lahza (إذا تم تفعيل التسجيل)
```
/path/to/whmcs/storage/logs/
```

---

## ⚡ معالجة سريعة للمدفوعات المعلقة

### إذا كان لديك مدفوعات ناجحة في Lahza.io ولكن غير مسجلة في WHMCS:

**1. احصل على بيانات المعاملة من Lahza.io Dashboard**

**2. استخدم المعالج اليدوي:**
```
https://yourdomain.com/lahza_manual_payment.php?process=lahza2024
```

**3. أدخل البيانات:**
- رقم الفاتورة
- رقم المعاملة من Lahza.io
- المبلغ
- الرسوم (إن وجدت)

**4. اضغط "معالجة الدفع"**

---

## 🛡️ الوقاية من المشاكل المستقبلية

### 1. مراقبة دورية
```
- فحص سجلات الـ webhook أسبوعياً
- مراقبة معدل نجاح المدفوعات
- اختبار الـ webhook شهرياً
```

### 2. إعدادات موصى بها
```
- تفعيل تسجيل الأحداث دائماً
- استخدام SSL certificate صالح
- تحديث API keys دورياً
```

### 3. نسخ احتياطية
```
- نسخ احتياطي من إعدادات البوابة
- حفظ API keys في مكان آمن
- توثيق إعدادات الـ webhook
```

---

## 📞 الحصول على المساعدة

### إذا استمرت المشكلة:

**1. اجمع المعلومات التالية:**
- نتائج أداة التشخيص
- سجلات الأخطاء
- لقطات شاشة من Lahza.io Dashboard
- تفاصيل المعاملة المتأثرة

**2. اتصل بالدعم الفني:**
- **Lahza.io Support**: <EMAIL>
- **WHMCS Support**: https://www.whmcs.com/support/

**3. موارد إضافية:**
- [Lahza.io Documentation](https://docs.lahza.io/)
- [WHMCS Payment Gateway Docs](https://docs.whmcs.com/Payment_Gateways)

---

## ⚠️ تحذيرات أمنية

### بعد حل المشكلة:
```
1. احذف ملفات التشخيص والإصلاح
2. غيّر كلمات المرور إذا لزم الأمر
3. راجع سجلات الوصول للتأكد من الأمان
```

### الملفات المؤقتة للحذف:
```
- lahza_webhook_debug.php
- lahza_webhook_fix.php
- lahza_manual_payment.php
- modules/gateways/callback/lahza_test.php
```

---

**آخر تحديث**: يناير 2025  
**الإصدار**: 3.0.0  
**للدعم الفني**: قم بإنشاء تقرير مفصل مع السجلات ورسائل الخطأ