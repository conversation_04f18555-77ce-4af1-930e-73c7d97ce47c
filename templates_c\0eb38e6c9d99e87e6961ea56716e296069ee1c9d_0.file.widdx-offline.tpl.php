<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:20:49
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\widdx-offline.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0f0197d6f2_45425300',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '0eb38e6c9d99e87e6961ea56716e296069ee1c9d' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\widdx-offline.tpl',
      1 => 1749349593,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0f0197d6f2_45425300 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE html>
<html lang="<?php echo $_smarty_tpl->tpl_vars['language']->value;?>
">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You're Offline - <?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            max-width: 600px;
            width: 100%;
            background: #fff;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .logo {
            max-width: 150px;
            height: auto;
            margin-bottom: 30px;
        }
        
        .offline-icon {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            color: #dc3545;
        }
        
        h1 {
            font-size: 2rem;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .lead {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: #fff;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 0 10px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-outline {
            background-color: transparent;
            border: 2px solid #007bff;
            color: #007bff;
        }
        
        .btn-outline:hover {
            background-color: #007bff;
            color: #fff;
        }
        
        .status-message {
            margin-top: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        @media (max-width: 576px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 1.75rem;
            }
            
            .lead {
                font-size: 1.1rem;
            }
            
            .btn {
                display: block;
                margin: 10px auto;
                max-width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/templates/<?php echo $_smarty_tpl->tpl_vars['template']->value;?>
/frontend/assets/img/logo.png" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="logo">
        <svg class="offline-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="1" y1="1" x2="23" y2="23"></line>
            <path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path>
            <path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path>
            <path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path>
            <path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path>
            <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
            <line x1="12" y1="20" x2="12" y2="20"></line>
        </svg>
        <h1><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'offline_title','default'=>"You're Offline"),$_smarty_tpl ) );?>
</h1>
        <p class="lead"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'offline_message','default'=>"It seems you've lost your internet connection. Don't worry, you can still access previously visited pages."),$_smarty_tpl ) );?>
</p>
        <button onclick="window.location.reload()" class="btn"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'retry_connection','default'=>"Retry Connection"),$_smarty_tpl ) );?>
</button>
        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
" class="btn btn-outline"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'return_home','default'=>"Return to Homepage"),$_smarty_tpl ) );?>
</a>
        <div class="status-message">
            <span id="status-text"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'checking_connection','default'=>"Checking connection status..."),$_smarty_tpl ) );?>
</span>
        </div>
    </div>
    
    <?php echo '<script'; ?>
>
        // Check online status periodically
        function checkOnlineStatus() {
            const statusText = document.getElementById('status-text');
            
            if (navigator.onLine) {
                statusText.textContent = '<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"online_status",'default'=>"You are back online!"),$_smarty_tpl ) );?>
';
                statusText.style.color = '#28a745';
            } else {
                statusText.textContent = '<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>"offline_status",'default'=>"You are currently offline"),$_smarty_tpl ) );?>
';
                statusText.style.color = '#dc3545';
            }
        }
        
        // Check status initially and when online/offline status changes
        window.addEventListener('online', checkOnlineStatus);
        window.addEventListener('offline', checkOnlineStatus);
        checkOnlineStatus();
        
        // Periodically check connection
        setInterval(checkOnlineStatus, 5000);
    <?php echo '</script'; ?>
>
</body>
</html>
<?php }
}
