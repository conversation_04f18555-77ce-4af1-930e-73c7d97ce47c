<?php
/**
 * WIDDX Enhanced Session Manager
 * Provides secure session management with proper validation
 */

class WiddxSessionManager {
    private static $instance = null;
    private $sessionStarted = false;
    private $sessionConfig = [
        'cookie_lifetime' => 0,
        'cookie_path' => '/',
        'cookie_domain' => '',
        'cookie_secure' => false,
        'cookie_httponly' => true,
        'cookie_samesite' => 'Lax',
        'use_strict_mode' => true,
        'use_cookies' => true,
        'use_only_cookies' => true,
        'cache_limiter' => 'nocache',
        'gc_maxlifetime' => 1440, // 24 minutes
        'gc_probability' => 1,
        'gc_divisor' => 100
    ];
    
    private function __construct() {
        $this->configureSession();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function configureSession() {
        // Set secure session configuration
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $this->sessionConfig['cookie_secure'] = true;
        }
        
        // Set domain for cookie
        if (isset($_SERVER['HTTP_HOST'])) {
            $host = $_SERVER['HTTP_HOST'];
            // Remove port if present
            $host = preg_replace('/:\d+$/', '', $host);
            $this->sessionConfig['cookie_domain'] = $host;
        }
        
        // Apply session configuration
        foreach ($this->sessionConfig as $key => $value) {
            ini_set('session.' . $key, $value);
        }
        
        // Set session name to something less predictable
        session_name('WIDDX_SESSION_' . substr(md5($_SERVER['HTTP_HOST'] ?? 'localhost'), 0, 8));
    }
    
    public function startSession() {
        if ($this->sessionStarted) {
            return true;
        }
        
        // Check if session is already started
        if (session_status() === PHP_SESSION_ACTIVE) {
            $this->sessionStarted = true;
            return true;
        }
        
        // Start session with error handling
        try {
            if (session_start()) {
                $this->sessionStarted = true;
                $this->validateSession();
                $this->regenerateSessionId();
                return true;
            }
        } catch (Exception $e) {
            WiddxErrorHandler::logCustomError('Session start failed: ' . $e->getMessage());
            return false;
        }
        
        return false;
    }
    
    private function validateSession() {
        // Validate session fingerprint
        $currentFingerprint = $this->generateFingerprint();
        
        if (!isset($_SESSION['_widdx_fingerprint'])) {
            $_SESSION['_widdx_fingerprint'] = $currentFingerprint;
            $_SESSION['_widdx_created'] = time();
            $_SESSION['_widdx_last_activity'] = time();
        } else {
            // Check if fingerprint matches
            if ($_SESSION['_widdx_fingerprint'] !== $currentFingerprint) {
                WiddxErrorHandler::logSecurityEvent('Session hijacking attempt detected', [
                    'expected_fingerprint' => $_SESSION['_widdx_fingerprint'],
                    'actual_fingerprint' => $currentFingerprint,
                    'session_id' => session_id()
                ]);
                $this->destroySession();
                return false;
            }
            
            // Check session timeout
            $maxLifetime = $this->sessionConfig['gc_maxlifetime'];
            if (time() - $_SESSION['_widdx_last_activity'] > $maxLifetime) {
                WiddxErrorHandler::logCustomError('Session expired due to inactivity');
                $this->destroySession();
                return false;
            }
            
            // Update last activity
            $_SESSION['_widdx_last_activity'] = time();
        }
        
        return true;
    }
    
    private function generateFingerprint() {
        $components = [
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            $this->getClientIP()
        ];
        
        return hash('sha256', implode('|', $components));
    }
    
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    private function regenerateSessionId() {
        // Regenerate session ID periodically for security
        if (!isset($_SESSION['_widdx_last_regeneration'])) {
            $_SESSION['_widdx_last_regeneration'] = time();
        } else {
            // Regenerate every 15 minutes
            if (time() - $_SESSION['_widdx_last_regeneration'] > 900) {
                session_regenerate_id(true);
                $_SESSION['_widdx_last_regeneration'] = time();
            }
        }
    }
    
    public function set($key, $value) {
        if (!$this->startSession()) {
            return false;
        }
        
        // Validate key
        if (!is_string($key) || empty($key)) {
            WiddxErrorHandler::logCustomError('Invalid session key provided: ' . var_export($key, true));
            return false;
        }
        
        // Prevent overwriting internal session variables
        if (strpos($key, '_widdx_') === 0) {
            WiddxErrorHandler::logSecurityEvent('Attempt to overwrite internal session variable', ['key' => $key]);
            return false;
        }
        
        $_SESSION[$key] = $value;
        return true;
    }
    
    public function get($key, $default = null) {
        if (!$this->startSession()) {
            return $default;
        }
        
        // Validate key
        if (!is_string($key) || empty($key)) {
            return $default;
        }
        
        return $_SESSION[$key] ?? $default;
    }
    
    public function has($key) {
        if (!$this->startSession()) {
            return false;
        }
        
        return isset($_SESSION[$key]);
    }
    
    public function remove($key) {
        if (!$this->startSession()) {
            return false;
        }
        
        // Validate key
        if (!is_string($key) || empty($key)) {
            return false;
        }
        
        // Prevent removing internal session variables
        if (strpos($key, '_widdx_') === 0) {
            WiddxErrorHandler::logSecurityEvent('Attempt to remove internal session variable', ['key' => $key]);
            return false;
        }
        
        if (isset($_SESSION[$key])) {
            unset($_SESSION[$key]);
            return true;
        }
        
        return false;
    }
    
    public function clear() {
        if (!$this->startSession()) {
            return false;
        }
        
        // Keep internal session variables
        $internalVars = [];
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, '_widdx_') === 0) {
                $internalVars[$key] = $value;
            }
        }
        
        $_SESSION = $internalVars;
        return true;
    }
    
    public function destroySession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Clear session data
            $_SESSION = [];
            
            // Delete session cookie
            if (ini_get('session.use_cookies')) {
                $params = session_get_cookie_params();
                setcookie(
                    session_name(),
                    '',
                    time() - 42000,
                    $params['path'],
                    $params['domain'],
                    $params['secure'],
                    $params['httponly']
                );
            }
            
            // Destroy session
            session_destroy();
        }
        
        $this->sessionStarted = false;
        return true;
    }
    
    public function getSessionInfo() {
        if (!$this->startSession()) {
            return null;
        }
        
        return [
            'session_id' => session_id(),
            'created' => $_SESSION['_widdx_created'] ?? null,
            'last_activity' => $_SESSION['_widdx_last_activity'] ?? null,
            'fingerprint' => $_SESSION['_widdx_fingerprint'] ?? null,
            'data_count' => count($_SESSION),
            'is_secure' => $this->sessionConfig['cookie_secure'],
            'lifetime' => $this->sessionConfig['gc_maxlifetime']
        ];
    }
    
    public function isSessionActive() {
        return $this->sessionStarted && session_status() === PHP_SESSION_ACTIVE;
    }
    
    public function setFlashMessage($type, $message) {
        if (!$this->startSession()) {
            return false;
        }
        
        if (!isset($_SESSION['_widdx_flash'])) {
            $_SESSION['_widdx_flash'] = [];
        }
        
        $_SESSION['_widdx_flash'][$type] = $message;
        return true;
    }
    
    public function getFlashMessage($type) {
        if (!$this->startSession()) {
            return null;
        }
        
        if (isset($_SESSION['_widdx_flash'][$type])) {
            $message = $_SESSION['_widdx_flash'][$type];
            unset($_SESSION['_widdx_flash'][$type]);
            return $message;
        }
        
        return null;
    }
    
    public function getAllFlashMessages() {
        if (!$this->startSession()) {
            return [];
        }
        
        $messages = $_SESSION['_widdx_flash'] ?? [];
        $_SESSION['_widdx_flash'] = [];
        return $messages;
    }
    
    public function setCSRFToken() {
        if (!$this->startSession()) {
            return false;
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['_widdx_csrf_token'] = $token;
        $_SESSION['_widdx_csrf_time'] = time();
        
        return $token;
    }
    
    public function validateCSRFToken($token) {
        if (!$this->startSession()) {
            return false;
        }
        
        if (!isset($_SESSION['_widdx_csrf_token']) || !isset($_SESSION['_widdx_csrf_time'])) {
            return false;
        }
        
        // Check token age (valid for 1 hour)
        if (time() - $_SESSION['_widdx_csrf_time'] > 3600) {
            unset($_SESSION['_widdx_csrf_token'], $_SESSION['_widdx_csrf_time']);
            return false;
        }
        
        // Use hash_equals to prevent timing attacks
        $isValid = hash_equals($_SESSION['_widdx_csrf_token'], $token);
        
        if ($isValid) {
            // Regenerate token after successful validation
            $this->setCSRFToken();
        }
        
        return $isValid;
    }
    
    public function getCSRFToken() {
        if (!$this->startSession()) {
            return null;
        }
        
        if (!isset($_SESSION['_widdx_csrf_token'])) {
            return $this->setCSRFToken();
        }
        
        return $_SESSION['_widdx_csrf_token'];
    }
}