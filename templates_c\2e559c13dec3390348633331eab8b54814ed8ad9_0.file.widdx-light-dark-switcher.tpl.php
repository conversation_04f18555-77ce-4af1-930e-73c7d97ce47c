<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:14
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\inc\widdx-light-dark-switcher.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c86eec959_64509826',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2e559c13dec3390348633331eab8b54814ed8ad9' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\inc\\widdx-light-dark-switcher.tpl',
      1 => 1746316350,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c86eec959_64509826 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Theme Toggle Button -->
<div class="ww-color-switch">
  <button type="button" class="ww-theme-toggle" aria-label="Toggle Dark/Light Mode" id="theme-toggle-btn">
    <div class="ww-theme-light" data-title="Dark">
      <i class="fas fa-moon"></i>
    </div>
    <div class="ww-theme-dark" data-title="Light">
      <i class="fas fa-sun"></i>
    </div>
  </button>
</div>

<!-- Inline script for immediate theme toggle functionality -->
<?php echo '<script'; ?>
>
  // Immediate theme toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    var toggleBtn = document.getElementById('theme-toggle-btn');
    if (toggleBtn) {
      toggleBtn.onclick = function(e) {
        e.preventDefault();
        var html = document.documentElement;
        var currentTheme = html.getAttribute('data-bs-theme') || 'light';
        var newTheme = currentTheme === 'light' ? 'dark' : 'light';

        html.setAttribute('data-bs-theme', newTheme);
        document.body.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        console.log('Theme toggled to:', newTheme);
      };
    }
  });
<?php echo '</script'; ?>
>
<?php }
}
