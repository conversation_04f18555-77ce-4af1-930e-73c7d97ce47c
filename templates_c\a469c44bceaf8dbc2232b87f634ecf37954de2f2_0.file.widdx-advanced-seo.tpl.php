<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:12:32
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\pages\seo-s\widdx-advanced-seo.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0d10ba23b3_37589405',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'a469c44bceaf8dbc2232b87f634ecf37954de2f2' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\pages\\seo-s\\widdx-advanced-seo.tpl',
      1 => 1749349593,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0d10ba23b3_37589405 (Smarty_Internal_Template $_smarty_tpl) {
?><section id="advanced-seo" style="padding: 20px; margin-bottom: 20px; border: 1px solid #ddd; background: #fff;">
    <h4>Advanced SEO</h4>
    <p>This involves optimizing more complex elements that affect the website's visibility in search engines. It
        includes:</p>
    <div class="row align-items-center">
        <div class="col-md-12 col-lg-12">
            <div class="accordion" id="accordionwiddxadvanced">

                <!-- Canonical Tag -->
                <div class="accordion-item">
                <h2 class="accordion-header" id="headingEight">
                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseEight" aria-expanded="true" aria-controls="collapseEight">
                        <i class="fas
                        <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['status'] == 'error') {?>
                            fa-times-circle text-danger
                        <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['status'] == 'warning') {?>
                            fa-exclamation-circle text-warning
                        <?php } else { ?>
                            fa-check-circle text-success
                        <?php }?> mr-2"></i>Canonical Tag
                    </button>
                </h2>
                <div id="collapseEight" class="accordion-collapse collapse show" aria-labelledby="headingEight" data-bs-parent="#accordionwiddxadvanced">
                    <div class="accordion-body">
                        <div class="alert alert-info"><?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['value'])===null||$tmp==='' ? 'No canonical tag found' : $tmp);?>
</div>
                        <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['status'] == 'error') {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['message'];?>
</p>
                        <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['message'];?>
</p>
                        <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> <?php echo $_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['canonicalTag']['message'];?>
</p>
                        <?php }?>
                    </div>
                </div>
            </div>
                <!-- Noindex -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingNine">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseNine" aria-expanded="false" aria-controls="collapseNine">
                            <i class="fas 
            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['noindex']['status'] == 'warning') {?>
                fa-exclamation-circle text-warning
            <?php } else { ?>
                fa-check-circle text-success
            <?php }?> mr-2"></i>Noindex
                        </button>
                    </h2>
                    <div id="collapseNine" class="accordion-collapse collapse" aria-labelledby="headingNine"
                        data-bs-parent="#accordionwiddxadvanced">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['noindex']['value'])===null||$tmp==='' ? 'No noindex detected' : $tmp);?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['noindex']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Noindex
                                detected. Ensure this is intentional.</p>
                            <p class="text-warning">The noindex tag prevents search engines from indexing this page.
                                Make sure this is what you want.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> No noindex tag
                                detected. This page will be indexed by search engines.</p>
                            <p class="text-success">Your page is set to be indexed by search engines.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- WWW Canonicalization -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTen">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseTen" aria-expanded="false" aria-controls="collapseTen">
                            <i class="fas 
            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['wwwCanonizalization']['status'] == 'error') {?>
                fa-times-circle text-danger
            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['wwwCanonizalization']['status'] == 'warning') {?>
                fa-exclamation-circle text-warning
            <?php } else { ?>
                fa-check-circle text-success
            <?php }?> mr-2"></i>WWW Canonicalization
                        </button>
                    </h2>
                    <div id="collapseTen" class="accordion-collapse collapse" aria-labelledby="headingTen"
                        data-bs-parent="#accordionwiddxadvanced">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['wwwCanonizalization']['value'])===null||$tmp==='' ? 'Not canonicalized' : $tmp);?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['wwwCanonizalization']['status'] == 'error') {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> WWW canonicalization
                                is not properly set up. Ensure redirections are correct.</p>
                            <p class="text-danger">Proper WWW canonicalization helps avoid duplicate content issues and
                                ensures a single version of your site is indexed.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['wwwCanonizalization']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Partial WWW
                                canonicalization detected. Ensure all URLs consistently use or omit WWW.</p>
                            <p class="text-warning">Consistent WWW canonicalization is important for avoiding duplicate
                                content issues.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> WWW
                                canonicalization is properly set up.</p>
                            <p class="text-success">Your WWW canonicalization is configured correctly, avoiding
                                duplicate content issues.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Robots.txt -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingEleven">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseEleven" aria-expanded="false" aria-controls="collapseEleven">
                            <i class="fas 
            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['robotsRules']['status'] == 'error') {?>
                fa-times-circle text-danger
            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['robotsRules']['status'] == 'warning') {?>
                fa-exclamation-circle text-warning
            <?php } else { ?>
                fa-check-circle text-success
            <?php }?> mr-2"></i>Robots.txt
                        </button>
                    </h2>
                    <div id="collapseEleven" class="accordion-collapse collapse" aria-labelledby="headingEleven"
                        data-bs-parent="#accordionwiddxadvanced">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php echo (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['robotsRules']['value'])===null||$tmp==='' ? 'No robots.txt found' : $tmp);?>
</div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['robotsRules']['status'] == 'error') {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No robots.txt file
                                found. Consider adding one to manage search engine crawling.</p>
                            <p class="text-danger">A robots.txt file helps manage which pages are crawled by search
                                engines.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['robotsRules']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Partially
                                configured robots.txt file. Review its rules to ensure proper crawling.</p>
                            <p class="text-warning">Ensure your robots.txt file is fully configured to manage search
                                engine crawling effectively.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> Robots.txt file
                                found. Review its rules to ensure proper crawling.</p>
                            <p class="text-success">Your robots.txt file is set up. It helps manage search engine
                                crawling effectively.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Open Graph Tags -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTwelve">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseTwelve" aria-expanded="false" aria-controls="collapseTwelve">
                            <i class="fas 
            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['openGraph']['status'] == 'error') {?>
                fa-times-circle text-danger
            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['openGraph']['status'] == 'warning') {?>
                fa-exclamation-circle text-warning
            <?php } else { ?>
                fa-check-circle text-success
            <?php }?> mr-2"></i>Open Graph Tags
                        </button>
                    </h2>
                    <div id="collapseTwelve" class="accordion-collapse collapse" aria-labelledby="headingTwelve"
                        data-bs-parent="#accordionwiddxadvanced">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, (($tmp = @$_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['openGraph']['value'])===null||$tmp==='' ? array() : $tmp), 'content', false, 'property');
$_smarty_tpl->tpl_vars['content']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['property']->value => $_smarty_tpl->tpl_vars['content']->value) {
$_smarty_tpl->tpl_vars['content']->do_else = false;
?>
                                <p><strong><?php echo $_smarty_tpl->tpl_vars['property']->value;?>
:</strong> <?php echo $_smarty_tpl->tpl_vars['content']->value;?>
</p>
                                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                            </div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['openGraph']['status'] == 'error') {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No Open Graph tags
                                found. Consider adding them for better social media integration.</p>
                            <p class="text-danger">Open Graph tags improve how your content appears when shared on
                                social media.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['openGraph']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Insufficient
                                Open Graph tags found. Add more to enhance social media integration.</p>
                            <p class="text-warning">Consider adding more Open Graph tags to provide better details when
                                your content is shared on social media.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> Open Graph tags are
                                present. Ensure they are accurate and optimized.</p>
                            <p class="text-success">Your Open Graph tags are set up. They help your content look better
                                on social media platforms.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>

                <!-- Schema Markup -->
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingThirteen">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                            data-bs-target="#collapseThirteen" aria-expanded="false" aria-controls="collapseThirteen">
                            <i class="fas 
            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['status'] == 'error') {?>
                fa-times-circle text-danger
            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['status'] == 'warning') {?>
                fa-exclamation-circle text-warning
            <?php } else { ?>
                fa-check-circle text-success
            <?php }?> mr-2"></i>Schema Markup
                        </button>
                    </h2>
                    <div id="collapseThirteen" class="accordion-collapse collapse" aria-labelledby="headingThirteen"
                        data-bs-parent="#accordionwiddxadvanced">
                        <div class="accordion-body">
                            <div class="alert alert-info">
                                <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['status'] != 'error') {?>
                                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['value'], 'schema');
$_smarty_tpl->tpl_vars['schema']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['schema']->value) {
$_smarty_tpl->tpl_vars['schema']->do_else = false;
?>
                                <pre><?php echo json_encode($_smarty_tpl->tpl_vars['schema']->value,JSON_PRETTY_PRINT);?>
</pre>
                                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                                <?php } else { ?>
                                No schema markup found
                                <?php }?>
                            </div>
                            <?php if ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['status'] == 'error') {?>
                            <p class="text-danger"><i class="fas fa-times-circle text-danger"></i> No schema markup
                                found. Consider adding it to improve search engine understanding of your content.</p>
                            <p class="text-danger">Schema markup helps search engines understand your content better and
                                can enhance search results.</p>
                            <?php } elseif ($_smarty_tpl->tpl_vars['seoResult']->value['advancedSeo']['schema']['status'] == 'warning') {?>
                            <p class="text-warning"><i class="fas fa-exclamation-circle text-warning"></i> Insufficient
                                schema markup found. Add more to enhance search engine understanding.</p>
                            <p class="text-warning">Consider adding more schema markup to provide better details for
                                search engines.</p>
                            <?php } else { ?>
                            <p class="text-success"><i class="fas fa-check-circle text-success"></i> Schema markup is
                                present. Ensure it is accurate and comprehensive.</p>
                            <p class="text-success">Your schema markup is set up. It helps search engines understand
                                your content and can improve search results.</p>
                            <?php }?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section><?php }
}
