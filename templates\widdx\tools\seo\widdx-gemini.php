<?php

function generateContentWithAI($prompt)
{
    // SECURITY NOTE: API key should be stored in environment variables or secure config
    $apiKey = 'AIzaSyDHX6NqUSt7t3_haV5HlQfiT4MbfmTCa18';
    $url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' . $apiKey;

    $data = json_encode([
        'contents' => [
            [
                'parts' => [
                    ['text' => $prompt]
                ]
            ]
        ]
    ]);

    // Use secure cURL instead of file_get_contents
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($data)
        ],
        CURLOPT_TIMEOUT => 30, // 30 second timeout for AI requests
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_USERAGENT => 'WIDDX SEO Analyzer/1.0',
        CURLOPT_FOLLOWLOCATION => false,
        CURLOPT_MAXREDIRS => 0,
        CURLOPT_PROTOCOLS => CURLPROTO_HTTPS, // Only HTTPS for API calls
    ]);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);

    if ($result === false || !empty($error)) {
        error_log("AI API cURL Error: " . $error);
        return 'Failed to generate content with AI - Connection error';
    }

    if ($httpCode !== 200) {
        error_log("AI API HTTP Error: " . $httpCode);
        return 'Failed to generate content with AI - API error';
    }

    $resultData = json_decode($result, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        error_log("AI API JSON Error: " . json_last_error_msg());
        return 'Failed to generate content with AI - Invalid response';
    }
    
    return $resultData['candidates'][0]['content']['parts'][0]['text'] ?? 'No content generated';
}

function generateAIPrompt($results)
{
    $prompt = "Analyze the following SEO data and provide detailed insights and recommendations:\n\n";
    $prompt .= "1. Search Preview:\n";
    $prompt .= "Title: " . $results['search_preview']['title'] . "\n";
    $prompt .= "Description: " . $results['search_preview']['description'] . "\n";
    $prompt .= "URL: " . $results['search_preview']['url'] . "\n\n";

    $prompt .= "2. Keywords:\n";
    $prompt .= implode(", ", array_keys(array_slice($results['keywords'], 0, 10))) . "\n\n";

    $prompt .= "3. Basic SEO:\n";
    foreach ($results['basic_seo'] as $key => $value) {
        $prompt .= ucfirst(str_replace('_', ' ', $key)) . ": " . (is_array($value) ? implode(", ", $value) : $value) . "\n";
    }
    $prompt .= "\n";

    $prompt .= "4. Advanced SEO:\n";
    foreach ($results['advanced_seo'] as $key => $value) {
        $prompt .= ucfirst(str_replace('_', ' ', $key)) . ": " . (is_array($value) ? implode(", ", $value) : $value) . "\n";
    }
    $prompt .= "\n";

    $prompt .= "5. Performance:\n";
    foreach ($results['performance'] as $key => $value) {
        $prompt .= ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
    }
    $prompt .= "\n";

    $prompt .= "6. Security:\n";
    foreach ($results['security'] as $key => $value) {
        $prompt .= ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
    }
    $prompt .= "\n";

    $prompt .= "7. SEO Scores:\n";
    foreach ($results['scores'] as $key => $value) {
        $prompt .= ucfirst(str_replace('_', ' ', $key)) . ": " . $value . "\n";
    }
    $prompt .= "\n";

    $prompt .= "Based on this data, provide a comprehensive SEO analysis, including strengths, weaknesses, and actionable recommendations for improvement. Focus on the most critical issues and opportunities for enhancement.";

    return $prompt;
}