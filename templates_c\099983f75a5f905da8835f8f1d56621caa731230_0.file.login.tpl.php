<?php
/* Smarty version 3.1.48, created on 2025-06-15 21:13:55
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\login.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f1b73a02610_82103856',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '099983f75a5f905da8835f8f1d56621caa731230' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\login.tpl',
      1 => 1747872401,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f1b73a02610_82103856 (Smarty_Internal_Template $_smarty_tpl) {
?><div class="container-fluid">
    <div class="row full-height">
        <!-- القسم الأيسر: بطاقة تسجيل الدخول -->
        <div class="col-md-6 login-section">
            <div class="card login-card">
                <div class="card-body">
                    <div class="app-brand text-center justify-content-center">
                        <a class="app-brand-link gap-2" href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/index.php">
                            <?php if ($_smarty_tpl->tpl_vars['assetLogoPath']->value) {?>
                                <img src="<?php echo $_smarty_tpl->tpl_vars['assetLogoPath']->value;?>
" alt="<?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
" class="logo-img">
                            <?php } else { ?>
                                <span class="app-brand-text text-body fw-bolder"><?php echo $_smarty_tpl->tpl_vars['companyname']->value;?>
</span>
                            <?php }?>
                        </a>
                    </div>
                    <h4 class="mb-2 text-center"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'loginbutton'),$_smarty_tpl ) );?>
</h4>
                    <p class="mb-4 text-center"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'userLogin.signInToContinue'),$_smarty_tpl ) );?>
</p>

                    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/flashmessage.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

                    <form id="formAuthentication" class="mb-3" action="<?php echo routePath('login-validate');?>
" method="POST"
                        role="form">
                        <div class="form-group">
                            <label for="inputEmail" class="form-control-label"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'clientareaemail'),$_smarty_tpl ) );?>
</label>
                            <div class="input-group input-group-merge">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <!-- إضافة placeholder -->
                                <input type="email" class="form-control" name="username" id="inputEmail"
                                    placeholder="<EMAIL>" autofocus required>
                            </div>
                        </div>

                        <div class="form-group mb-4 focused">
                            <div class="d-flex align-items-center justify-content-between">
                                <label for="inputPassword"
                                    class="form-control-label"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'clientareapassword'),$_smarty_tpl ) );?>
</label>
                                <div class="mb-2">
                                    <a href="<?php echo routePath('password-reset-begin');?>
" class="small text-muted"
                                        tabindex="-1">
                                        <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'forgotpw'),$_smarty_tpl ) );?>

                                    </a>
                                </div>
                            </div>
                            <div class="input-group input-group-merge">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-key"></i></span>
                                </div>
                                <!-- إضافة placeholder -->
                                <input type="password" class="form-control pw-input" name="password" id="inputPassword"
                                    placeholder="<?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'clientareapassword'),$_smarty_tpl ) );?>
" autocomplete="off" required>
                                <div class="input-group-append">
                                    <!-- تحسين زر عرض/إخفاء كلمة المرور مع Tooltip -->
                                    <button class="btn btn-default btn-reveal-pw" type="button" tabindex="-1"
                                        onclick="togglePassword()" data-bs-toggle="tooltip" title="Show/Hide Password">
                                        <i class="fas fa-eye" id="toggleIcon"></i>
                                    </button>

                                </div>
                            </div>
                        </div>

                        <?php if ($_smarty_tpl->tpl_vars['captcha']->value->isEnabled()) {?>
                            <div class="form-group mb-4">
                                <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/captcha.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
                            </div>
                        <?php }?>

                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" name="rememberme" />
                                <label class="form-check-label"><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'loginrememberme'),$_smarty_tpl ) );?>
</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <button id="login" type="submit"
                                class="btn btn-primary d-grid w-100<?php echo $_smarty_tpl->tpl_vars['captcha']->value->getButtonClass($_smarty_tpl->tpl_vars['captchaForm']->value);?>
">
                                <?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'loginbutton'),$_smarty_tpl ) );?>

                            </button>
                        </div>
                    </form>

                    <p class="text-center">
                        <span><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'userLogin.notRegistered'),$_smarty_tpl ) );?>
</span>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/register.php">
                            <span><?php echo call_user_func_array( $_smarty_tpl->smarty->registered_plugins[Smarty::PLUGIN_FUNCTION]['lang'][0], array( array('key'=>'userLogin.createAccount'),$_smarty_tpl ) );?>
</span>
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <!-- القسم الأيمن: الكلمات المتحركة -->
        <div class="col-md-6 animated-section">
            <div class="animated-title">
                <div class="text-top">
                    <div>
                        <span>Welcome to Your</span>
                        <span class="future"></span>
                    </div>
                </div>
                <div class="text-bottom">
                    <div>With WIDDX</div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/includes/linkedaccounts.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array('linkContext'=>"login",'customFeedback'=>true), 0, true);
?>

<style>
 

    .container-fluid {
        height: 100%;
        padding: 0;
    }

    .full-height {
        height: 100%;
    }

    .login-section,
    .animated-section {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }

    .login-card {
        width: 100%;
        max-width: 400px;
        margin: 5px;
    }

    /* تحسين تفاعل الحقول عند التركيز */
    .form-control:focus {
        border-color: #5A67D8;
        box-shadow: 0 0 0 0.2rem rgba(90, 103, 216, 0.25);
    }

    /* تحسين سلاسة تغيير الأيقونة */
    .btn-reveal-pw i {
        transition: transform 0.3s ease-in-out;
    }

    .btn-reveal-pw:hover i {
        transform: scale(1.2);
    }

    /* Styles for the animated title */
    @keyframes showTopText {
        0% {
            transform: translate3d(0, 100%, 0);
        }

        40%,
        60% {
            transform: translate3d(0, 50%, 0);
        }

        100% {
            transform: translate3d(0, 0, 0);
        }

        animation-timing-function: ease-in-out;
    }

    @keyframes showBottomText {
        0% {
            transform: translate3d(0, -100%, 0);
        }

        100% {
            transform: translate3d(0, 0, 0);
        }

        animation-timing-function: ease-in-out;
    }

    .animated-title {
        color: #222;
        font-family: Roboto, Arial, sans-serif;
        height: 90vmin;
        position: relative;
        width: 90vmin;
    }

    .animated-title>div {
        height: 50%;
        overflow: hidden;
        position: absolute;
        width: 100%;
    }

    .animated-title>div div {
        font-size: 8vmin;
        padding: 2vmin 0;
        position: absolute;
    }

    .animated-title>div div span {
        display: block;
    }

    .animated-title>div.text-top {
        border-bottom: 1vmin solid #000;
        top: 0;
    }

    .animated-title>div.text-top div {
        animation: showTopText 0.7s;
        animation-delay: 0.1s;
        animation-fill-mode: forwards;
        bottom: 0;
        transform: translate(0, 100%);
    }

    .animated-title>div.text-top div span:first-child {
        color: #767676;
    }

    .animated-title>div.text-bottom {
        bottom: 0;
    }

    .animated-title>div.text-bottom div {
        animation: showBottomText 0.5s;
        animation-delay: 1.75s;
        animation-fill-mode: forwards;
        top: 0;
        transform: translate(0, -100%);
    }

    /* Hide default header and footer */
    .header.header,
    .footer.footer {
        display: none;
    }

    /* تحسين التفاعل عند ملء الحقول */
    .form-control:not(:placeholder-shown) {
        border-color: #48BB78;
        box-shadow: 0 0 5px rgba(72, 187, 120, 0.5);
    }

    @media (max-width: 767px) {
        .animated-section {
            display: none;
        }

        .login-section {
            width: 100%;
            padding: 20px;
        }

        .login-card {
            max-width: 100%;
        }

        .form-control {
            font-size: 1.2rem;
        }

        .btn-primary {
            font-size: 1.1rem;
        }
    }

    .future {
        font-size: 1.5em;
        /* تغيير حجم الخط هنا */
        display: inline-block;
        white-space: nowrap;
        /* منع الانتقال إلى سطر جديد */
    }

    @keyframes reveal {
        0% {
            opacity: 0;
            transform: translateY(30px);
            /* تحريكها إلى الأسفل */
        }

        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* إضافة تأثير لكل حرف */
    .future span {
        display: inline-block;
        animation: fadeIn 0.5s forwards;
    }

    @keyframes fadeIn {
        0% {
            opacity: 0;
            transform: translateY(3px);
            /* تحريك كل حرف إلى الأسفل */
        }

        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Future */
    .text-top div .future {
        height: 132px;
        text-align: justify;
        text-transform: uppercase;
        font-family: 'Cairo', sans-serif;
        font-weight: 700;
        font-size: 9rem;
    }

    /* Span Tag */
    #main-body .container .row .primary-content .container-fluid .full-height .animated-section .animated-title .text-top div span {
        line-height: 1em !important;
    }

    /* Division */
    #main-body .text-bottom div {
        font-weight: 600;
        text-transform: uppercase;
        line-height: 1.2em;
        font-size: 87px;
    }

    /* Span Tag */
    .text-top div span {
        font-family: 'Cairo', sans-serif;
    }
</style>

<?php echo '<script'; ?>
>
    function togglePassword() {
        var passwordInput = document.getElementById('inputPassword');
        var toggleIcon = document.getElementById('toggleIcon');
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Initialize tooltips for bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });


    const futureText = "Future";
    const futureContainer = document.querySelector('.future');
    let index = 0;

    function typeNextLetter() {
        if (index < futureText.length) {
            futureContainer.textContent += futureText.charAt(index);
            index++;
            setTimeout(typeNextLetter, 300); // تأخير 300 مللي ثانية بين كل حرف
        }
    }
    typeNextLetter(); // ابدأ الكتابة
<?php echo '</script'; ?>
><?php }
}
