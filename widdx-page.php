<?php

use WHMC<PERSON>\ClientArea;

define('CLIENTAREA', true);

require __DIR__ . '/init.php';

// Use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
$pageName = filter_input(INPUT_GET, 'page', FILTER_SANITIZE_FULL_SPECIAL_CHARS) ?? 'default';
$action = filter_input(INPUT_GET, 'action', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

// Additional validation for page name
$pageName = preg_replace('/[^a-z0-9\-]/', '', strtolower($pageName));
$action = $action ? preg_replace('/[^a-z0-9_\-]/', '', strtolower($action)) : null;

$allowedPages = [
    'affiliate-program',
    'hosting-service-or-domain-transfer-tod',
    'hosting-terms-of-service-tos',
    'privacy-policy',
    'reseller-hosting-terms-of-service-tos',
    'terms-of-service',
    'terms-and-conditions',
    'return-policy',
    'dispute-resolution',
    'offline',
    'blog',
    'whois-checker',
    'seo-analyzer',
    'sitemap-generator',
    'payment-success',
];

if (!preg_match('/^[a-z0-9\-]+$/', $pageName) || !in_array($pageName, $allowedPages)) {
    $pageName = 'offline';
}

$ca = new ClientArea();

try {
    $ca->setPageTitle(ucwords(str_replace('-', ' ', $pageName)));

    $templateName = 'widdx-' . $pageName;
    $templatePath = __DIR__ . '/templates/widdx/frontend/pages/' . $templateName . '.tpl';

    if (file_exists($templatePath)) {
        if ($pageName === 'seo-analyzer') {
            require __DIR__ . '/templates/widdx/tools/widdx-seo-analyzer.php';
            $seoResult = handleSeoAnalyzer();

            if ($action === 'export_pdf' && isset($seoResult['analysisResult'])) {
                $pdfContent = generatePDF($seoResult['analysisResult'], $seoResult['seoScore'], $seoResult['url']);
                header('Content-Type: application/pdf');
                header('Content-Disposition: attachment; filename="seo_analysis_report.pdf"');
                echo $pdfContent;
                exit;
            }

            $ca->assign('seoResult', $seoResult['analysisResult']);
            $ca->assign('seoScore', $seoResult['seoScore']);
            $ca->assign('faviconUrl', $seoResult['faviconUrl']);
            $ca->assign('url', $seoResult['url']);
        } elseif ($pageName === 'whois-checker') {
            require __DIR__ . '/templates/widdx/tools/widdx-whois-checker.php';
            $whoisResult = handleWhoisCheck();

            if ($action === 'check_whois' && !empty($_POST['domain'])) {
                // If it's an AJAX request, return only the WHOIS result
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    echo $whoisResult;
                    exit;
                }
            }

            $ca->assign('whoisResult', $whoisResult);
        }

        $ca->initPage();
        $ca->setTemplate('frontend/pages/' . $templateName);
    } else {
        $ca->setTemplate('frontend/pages/widdx-offline');
    }

    $ca->output();
} catch (Exception $e) {
    error_log($e->getMessage());
    $ca->setPageTitle('Error');
    $ca->initPage();
    $ca->setTemplate('frontend/pages/widdx-error');
    $ca->output();
}