<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:17:10
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\inc\social-accounts.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0e26834342_08245559',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '36540d0c94a83ad5ece52d9c802d0f16d9bddb34' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\inc\\social-accounts.tpl',
      1 => **********,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0e26834342_08245559 (Smarty_Internal_Template $_smarty_tpl) {
?><ul class="list-inline social-icons">
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['socialAccounts']->value, 'account');
$_smarty_tpl->tpl_vars['account']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['account']->value) {
$_smarty_tpl->tpl_vars['account']->do_else = false;
?>
        <li class="list-inline-item">
            <a class="btn  btn-icon rounded-circle p-2" href="<?php echo $_smarty_tpl->tpl_vars['account']->value->getUrl();?>
" target="_blank">
                <i class="<?php echo $_smarty_tpl->tpl_vars['account']->value->getFontAwesomeIcon();?>
"></i>
            </a>
        </li>
    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</ul>
<style>

</style><?php }
}
