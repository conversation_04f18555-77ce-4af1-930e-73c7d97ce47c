<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:15
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\partial\widdx-navbar.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c87b3bf08_47750521',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '2a0e3fd6af7dd44da1e4130edf1d00c96b634d69' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\partial\\widdx-navbar.tpl',
      1 => 1747864158,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c87b3bf08_47750521 (Smarty_Internal_Template $_smarty_tpl) {
?><nav class="layout-navbar container-xxl navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme shadow-sm"
  id="layout-navbar">
  <!-- Menu Toggle (Visible on small screens) -->
  <div class="layout-menu-toggle navbar-nav align-items-xl-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3 ms-xl-0<?php } else { ?>me-3 me-xl-0<?php }?> d-xl-none">
    <a class="nav-item nav-link px-0 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-xl-4<?php } else { ?>me-xl-4<?php }?>" href="javascript:void(0)">
      <div class="menu-toggle-icon">
        <i class="bx bx-menu bx-sm"></i>
      </div>
    </a>
  </div>

  <!-- Company Logo (Centered on mobile) -->
  <div class="navbar-brand mx-auto d-xl-none">
    <img src="<?php echo $_smarty_tpl->tpl_vars['WEB_ROOT']->value;?>
/assets/img/logo.png" alt="Company Logo" height="30" class="logo-img">
  </div>

  <div class="layout-overlay"></div>

  <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
    <!-- Search -->
    <div class="navbar-search hide-on-mobile">
      <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-search.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
    </div>
    <!-- /Search -->
    <!-- Search Mobile -->
    <div class="navbar-search hide-on-pc">
      <div class="main-nav-search">
        <button class="search-toggle-btn" id="main-nav-search-btn">
          <i class="fa fa-search"></i>
        </button>
      </div>
    </div>
    <!-- /Search Mobile -->

    <ul class="navbar-nav flex-row align-items-center <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>me-auto<?php } else { ?>ms-auto<?php }?>">
      <!-- Language/Currency Selector -->
      <?php if ($_smarty_tpl->tpl_vars['languagechangeenabled']->value && count($_smarty_tpl->tpl_vars['locales']->value) > 1 || $_smarty_tpl->tpl_vars['currencies']->value) {?>
      <li class="nav-item lh-1 <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ms-3<?php } else { ?>me-3<?php }?>">
        <button type="button" class="btn nav-btn lang-selector" data-toggle="modal" data-target="#modalChooseLanguage">
          <div class="d-inline-block align-middle">
            <div class="iti-flag <?php if ($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'] === 'GB') {?>us<?php } else {
echo mb_strtolower($_smarty_tpl->tpl_vars['activeLocale']->value['countryCode'], 'UTF-8');
}?>">
            </div>
          </div>
        </button>
      </li>
      <?php }?>

      <!-- Quick Actions -->
      <li class="nav-item topbar-icon hide-on-mobile">
        <a class="nav-link nav-btn" data-bs-toggle="modal" data-bs-target="#quickActionsModal">
          <div class="nav-icon-circle">
            <i class="fas fa-layer-group"></i>
          </div>
        </a>
      </li>

      <!-- Cart -->
      <li class="nav-item hide-on-mobile">
        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-cart.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
      </li>

      <!-- Notifications -->
      <li class="nav-item hide-on-mobile">
        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-notification.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
      </li>

      <!-- Theme Toggle -->
      <li class="nav-item hide-on-mobile">
        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-theme-toggle.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
      </li>

      <!-- User Profile -->
      <li class="nav-item">
        <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-profile.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>
      </li>
    </ul>
  </div>
</nav>

<!-- Client email is already defined in the main layout -->

<?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/widdx-quick.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
$_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widget/mobile/widdx-search-icon.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

<style>
/* Modern Navbar Styling */
.layout-navbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 0.5rem;
  margin-top: 0.75rem;
  transition: all 0.3s ease;
}

/* Menu Toggle Button */
.menu-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  transition: all 0.3s ease;
}

.menu-toggle-icon:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
}

/* Logo Styling */
.logo-img {
  transition: all 0.3s ease;
}

.logo-img:hover {
  transform: scale(1.05);
}

/* Nav Buttons */
.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--bs-body-color);
  transition: all 0.3s ease;
  border: none;
  padding: 0;
}

.nav-btn:hover, .nav-btn:focus {
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  transform: scale(1.05);
}

/* Nav Icon Circle */
.nav-icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Search Toggle Button */
.search-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--bs-primary);
  border: none;
  transition: all 0.3s ease;
}

.search-toggle-btn:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
}

/* Language Selector */
.lang-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 38px;
  border-radius: 50%;
  background-color: rgba(var(--bs-primary-rgb), 0.1);
  transition: all 0.3s ease;
  border: none;
  padding: 0;
}

.lang-selector:hover {
  background-color: rgba(var(--bs-primary-rgb), 0.2);
  transform: scale(1.05);
}

/* Dark Mode Adjustments */
[data-bs-theme="dark"] .menu-toggle-icon,
[data-bs-theme="dark"] .search-toggle-btn,
[data-bs-theme="dark"] .lang-selector {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--bs-primary);
}

[data-bs-theme="dark"] .menu-toggle-icon:hover,
[data-bs-theme="dark"] .search-toggle-btn:hover,
[data-bs-theme="dark"] .lang-selector:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Mobile Adjustments */
@media (max-width: 1199.98px) {
  .navbar-brand {
    position: absolute;
    <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>
    right: 50%;
    transform: translateX(50%);
    <?php } else { ?>
    left: 50%;
    transform: translateX(-50%);
    <?php }?>
  }

  /* Adjust other elements to make space for the centered logo */
  .layout-menu-toggle {
    <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>
    margin-left: auto;
    <?php } else { ?>
    margin-right: auto;
    <?php }?>
  }

  #navbar-collapse {
    <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>
    justify-content: flex-start;
    <?php } else { ?>
    justify-content: flex-end;
    <?php }?>
  }

  .layout-navbar {
    margin-top: 0.5rem;
    border-radius: 0.375rem;
  }
}
</style><?php }
}
