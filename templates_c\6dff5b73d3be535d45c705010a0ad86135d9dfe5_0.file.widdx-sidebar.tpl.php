<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:10:18
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\backend\inc\widdx-sidebar.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0c8af3cb63_88417120',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6dff5b73d3be535d45c705010a0ad86135d9dfe5' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\backend\\inc\\widdx-sidebar.tpl',
      1 => 1747855062,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0c8af3cb63_88417120 (Smarty_Internal_Template $_smarty_tpl) {
?><style>
    .collapsable-card-body {
        display: block;
        /* جعل المحتوى مفتوحًا بشكل افتراضي */
    }

    .card-header .card-minimise {
        cursor: pointer;
    }

    .card-header i {
        transition: transform 0.3s ease;
    }

    .card-header i.rotate {
        transform: rotate(180deg);
    }

    .card-footer {
        background-color: #f8f9fa;
    }

    .mobile-select-card {
        display: none;
    }

    @media (max-width: 768px) {
        .mobile-select-card {
            display: block;
        }
    }
</style>

<!-- Sidebar script initialization -->
<?php $_smarty_tpl->_assignInScope('menuIcons', array('hosting'=>'fas fa-server','domains'=>'fas fa-globe','billing'=>'fas fa-file-invoice-dollar','support'=>'fas fa-headset','tickets'=>'fas fa-ticket-alt','store'=>'fas fa-shopping-cart','security'=>'fas fa-shield-alt','email'=>'fas fa-envelope','ssl'=>'fas fa-lock','dns'=>'fas fa-network-wired','website'=>'fas fa-desktop','database'=>'fas fa-database','backup'=>'fas fa-backup','settings'=>'fas fa-cog','user'=>'fas fa-user','affiliate'=>'fas fa-users','reports'=>'fas fa-chart-bar','tools'=>'fas fa-tools','home'=>'fas fa-home','dashboard'=>'fas fa-tachometer-alt','announcements'=>'fas fa-bullhorn','knowledgebase'=>'fas fa-book','downloads'=>'fas fa-download','network'=>'fas fa-network-wired','default'=>'fas fa-angle-right'));?>

<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['sidebar']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?>
    <div menuItemName="<?php echo $_smarty_tpl->tpl_vars['item']->value->getName();?>
"
        class="mb-3 card card-sidebar<?php if ($_smarty_tpl->tpl_vars['item']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['item']->value->getClass();
}
if ($_smarty_tpl->tpl_vars['item']->value->getExtra('mobileSelect') && $_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?> d-none d-md-block<?php }?>"
        <?php if ($_smarty_tpl->tpl_vars['item']->value->getAttribute('id')) {?> id="<?php echo $_smarty_tpl->tpl_vars['item']->value->getAttribute('id');?>
" <?php }?>>
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3 class="card-title m-0">
                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasIcon()) {?><i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getIcon();?>
"></i><?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>&nbsp;<?php } else { ?>&nbsp;<?php }
}?>
                <?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>

                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBadge()) {
if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>&nbsp;<span class="badge badge-primary float-left"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php } else { ?>&nbsp;<span class="badge badge-primary float-right"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php }
}?>
            </h3>
            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
                <i class="fas fa-chevron-up card-minimise"></i>
            <?php }?>
        </div>
        <div class="collapsable-card-body">
            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBodyHtml()) {?>
                <div class="card-body">
                    <?php echo $_smarty_tpl->tpl_vars['item']->value->getBodyHtml();?>

                </div>
            <?php }?>
            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
                <div class="list-group list-group-flush d-md-flex<?php if ($_smarty_tpl->tpl_vars['item']->value->getChildrenAttribute('class')) {?> <?php echo $_smarty_tpl->tpl_vars['item']->value->getChildrenAttribute('class');
}?>"
                    role="tablist">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getUri()) {?>
                            <a menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
" href="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
"
                                class="list-group-item list-group-item-action<?php if ($_smarty_tpl->tpl_vars['childItem']->value->isDisabled()) {?> disabled<?php }
if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();
}
if ($_smarty_tpl->tpl_vars['childItem']->value->isCurrent()) {?> active<?php }?>"
                                <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('dataToggleTab')) {?> data-toggle="list" role="tab" <?php }?>
                                <?php $_smarty_tpl->_assignInScope('customActionData', $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('dataCustomAction'));?>
                                <?php if (is_array($_smarty_tpl->tpl_vars['customActionData']->value)) {?> data-active="<?php echo $_smarty_tpl->tpl_vars['customActionData']->value['active'];?>
"
                                    data-identifier="<?php echo $_smarty_tpl->tpl_vars['customActionData']->value['identifier'];?>
" data-serviceid="<?php echo $_smarty_tpl->tpl_vars['customActionData']->value['serviceid'];?>
"
                                    <?php }?> <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target');?>
" <?php }?>
                                    id="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getId();?>
">
                                    <div class="d-flex align-items-center">
                                        <div class="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ml-2<?php } else { ?>mr-2<?php }?>">
                                            <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?>
                                                <i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
"></i>
                                            <?php } else { ?>
                                                <?php $_smarty_tpl->_assignInScope('itemName', mb_strtolower($_smarty_tpl->tpl_vars['childItem']->value->getName(), 'UTF-8'));?>
                                                <?php $_smarty_tpl->_assignInScope('itemIcon', (($tmp = @$_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value])===null||$tmp==='' ? $_smarty_tpl->tpl_vars['menuIcons']->value['default'] : $tmp));?>
                                                <i class="<?php echo $_smarty_tpl->tpl_vars['itemIcon']->value;?>
"></i>
                                            <?php }?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>

                                            <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?><span
                                                class="badge badge-secondary"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span><?php }?>
                                        </div>
                                        <?php if (strpos($_smarty_tpl->tpl_vars['childItem']->value->getClass(),'menu-link menu-toggle') !== false) {?>
                                            <div class="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>mr-2<?php } else { ?>ml-2<?php }?>">
                                                <i class="fas <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>fa-chevron-left<?php } else { ?>fa-chevron-right<?php }?> menu-arrow"></i>
                                            </div>
                                        <?php }?>
                                    </div>
                                </a>
                            <?php } else { ?>
                                <div menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
"
                                    class="list-group-item list-group-item-action<?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();
}?>"
                                    id="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getId();?>
">
                                    <div class="d-flex align-items-center">
                                        <div class="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>ml-2<?php } else { ?>mr-2<?php }?>">
                                            <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?>
                                                <i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
"></i>
                                            <?php } else { ?>
                                                <?php $_smarty_tpl->_assignInScope('itemName', mb_strtolower($_smarty_tpl->tpl_vars['childItem']->value->getName(), 'UTF-8'));?>
                                                <?php $_smarty_tpl->_assignInScope('itemIcon', (($tmp = @$_smarty_tpl->tpl_vars['menuIcons']->value[$_smarty_tpl->tpl_vars['itemName']->value])===null||$tmp==='' ? $_smarty_tpl->tpl_vars['menuIcons']->value['default'] : $tmp));?>
                                                <i class="<?php echo $_smarty_tpl->tpl_vars['itemIcon']->value;?>
"></i>
                                            <?php }?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>

                                            <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?><span
                                                class="badge badge-secondary"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span><?php }?>
                                        </div>
                                        <?php if (strpos($_smarty_tpl->tpl_vars['childItem']->value->getClass(),'menu-link menu-toggle') !== false) {?>
                                            <div class="<?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>mr-2<?php } else { ?>ml-2<?php }?>">
                                                <i class="fas <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>fa-chevron-left<?php } else { ?>fa-chevron-right<?php }?> menu-arrow"></i>
                                            </div>
                                        <?php }?>
                                    </div>
                                </div>
                            <?php }?>
                        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                    </div>
                <?php }?>
            </div>
            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasFooterHtml()) {?>
                <div class="card-footer clearfix">
                    <?php echo $_smarty_tpl->tpl_vars['item']->value->getFooterHtml();?>

                </div>
            <?php }?>
        </div>

        <?php if ($_smarty_tpl->tpl_vars['item']->value->getExtra('mobileSelect') && $_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
            <div class="card mobile-select-card">
                <div class="card-header">
                    <h3 class="card-title">
                        <?php if ($_smarty_tpl->tpl_vars['item']->value->hasIcon()) {?><i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getIcon();?>
"></i><?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>&nbsp;<?php } else { ?>&nbsp;<?php }
}?>
                        <?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>

                        <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBadge()) {
if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>&nbsp;<span class="badge badge-primary float-left"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php } else { ?>&nbsp;<span class="badge badge-primary float-right"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php }
}?>
                    </h3>
                </div>
                <div class="card-body">
                    <form role="form">
                        <select class="form-control" onchange="selectChangeNavigate(this)">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                                <option menuItemName="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getName();?>
" value="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
"
                                    <?php if ($_smarty_tpl->tpl_vars['childItem']->value->isCurrent()) {?>selected="selected" <?php }?>>
                                    <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>

                                    <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?>(<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
)<?php }?>
                                </option>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </select>
                    </form>
                </div>
                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasFooterHtml()) {?>
                    <div class="card-footer">
                        <?php echo $_smarty_tpl->tpl_vars['item']->value->getFooterHtml();?>

                    </div>
                <?php }?>
            </div>
        <?php }?>
    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>

<style>
    .menu-arrow {
        transition: transform 0.3s ease;
    }

    <?php if ($_smarty_tpl->tpl_vars['language']->value == 'arabic' || $_smarty_tpl->tpl_vars['language']->value == 'hebrew' || $_smarty_tpl->tpl_vars['language']->value == 'farsi') {?>
    .menu-link.menu-toggle.active .menu-arrow {
        transform: rotate(-90deg);
    }
    <?php } else { ?>
    .menu-link.menu-toggle.active .menu-arrow {
        transform: rotate(90deg);
    }
    <?php }?>
</style>

<?php echo '<script'; ?>
>
    jQuery(document).ready(function($) {
        $('.menu-link.menu-toggle').on('click', function(e) {
            e.preventDefault();
            $(this).toggleClass('active');
            $(this).find('.menu-arrow').toggleClass('rotate');
        });
    });
<?php echo '</script'; ?>
>
<?php }
}
