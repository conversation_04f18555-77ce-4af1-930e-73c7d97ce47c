<?php
/* Smarty version 3.1.48, created on 2025-06-15 20:17:10
  from 'C:\xampp\htdocs\Whmcs\templates\widdx\frontend\inc\widdx-header-theme-switcher.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_684f0e2679c193_63956488',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'd6d1b8662a12419d302569747df8c4bf453cb452' => 
    array (
      0 => 'C:\\xampp\\htdocs\\Whmcs\\templates\\widdx\\frontend\\inc\\widdx-header-theme-switcher.tpl',
      1 => 1747703291,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_684f0e2679c193_63956488 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Header Theme Toggle Button -->
<div class="ww-color-switch-header">
  <button type="button" class="ww-theme-toggle-header" aria-label="Toggle Dark/Light Mode" id="header-theme-toggle-btn">
    <div class="ww-theme-light" data-title="Dark">
      <i class="fas fa-moon"></i>
    </div>
    <div class="ww-theme-dark" data-title="Light">
      <i class="fas fa-sun"></i>
    </div>
  </button>
</div>

<!-- Inline script for immediate theme toggle functionality -->
<?php echo '<script'; ?>
>
  // Immediate theme toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    var toggleBtn = document.getElementById('header-theme-toggle-btn');
    if (toggleBtn) {
      toggleBtn.onclick = function(e) {
        e.preventDefault();
        var html = document.documentElement;
        var currentTheme = html.getAttribute('data-bs-theme') || 'light';
        var newTheme = currentTheme === 'light' ? 'dark' : 'light';

        html.setAttribute('data-bs-theme', newTheme);
        document.body.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        console.log('Theme toggled to:', newTheme);
      };
    }
  });
<?php echo '</script'; ?>
>
<?php }
}
