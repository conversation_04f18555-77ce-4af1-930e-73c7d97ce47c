<?php
/**
 * WIDDX Security Configuration
 * Enhanced security settings and configurations
 */

// Prevent direct access
if (!defined('WHMCS')) {
    die('Access denied');
}

class WiddxSecurity {
    
    /**
     * Security Headers Configuration
     */
    public static function setSecurityHeaders() {
        // Only set headers if not already sent
        if (!headers_sent()) {
            // Content Security Policy
            header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self' https:; frame-ancestors 'none';");
            
            // X-Frame-Options
            header('X-Frame-Options: DENY');
            
            // X-Content-Type-Options
            header('X-Content-Type-Options: nosniff');
            
            // X-XSS-Protection
            header('X-XSS-Protection: 1; mode=block');
            
            // Referrer Policy
            header('Referrer-Policy: strict-origin-when-cross-origin');
            
            // Permissions Policy
            header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
            
            // HSTS (only for HTTPS)
            if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
                header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
            }
            
            // Remove server information
            header_remove('X-Powered-By');
            header_remove('Server');
        }
    }
    
    /**
     * Input Validation and Sanitization
     */
    public static function sanitizeInput($input, $type = 'string') {
        if (is_array($input)) {
            return array_map(function($item) use ($type) {
                return self::sanitizeInput($item, $type);
            }, $input);
        }
        
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_SANITIZE_EMAIL);
                
            case 'url':
                return filter_var($input, FILTER_SANITIZE_URL);
                
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
                
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
                
            case 'domain':
                // Remove protocol and www
                $input = preg_replace('#^https?://#', '', $input);
                $input = preg_replace('/^www\./', '', $input);
                // Allow only valid domain characters
                return preg_replace('/[^a-zA-Z0-9\-\.]/', '', $input);
                
            case 'filename':
                // Remove path traversal attempts and dangerous characters
                $input = basename($input);
                return preg_replace('/[^a-zA-Z0-9\-_\.]/', '', $input);
                
            case 'alphanumeric':
                return preg_replace('/[^a-zA-Z0-9]/', '', $input);
                
            case 'string':
            default:
                // Use FILTER_SANITIZE_FULL_SPECIAL_CHARS instead of deprecated FILTER_SANITIZE_STRING
                return filter_var($input, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
        }
    }
    
    /**
     * Validate Input
     */
    public static function validateInput($input, $type, $options = []) {
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
                
            case 'url':
                return filter_var($input, FILTER_VALIDATE_URL) !== false;
                
            case 'ip':
                return filter_var($input, FILTER_VALIDATE_IP) !== false;
                
            case 'domain':
                return preg_match('/^[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,}$/', $input);
                
            case 'int':
                $min = $options['min'] ?? null;
                $max = $options['max'] ?? null;
                $flags = 0;
                $filterOptions = [];
                
                if ($min !== null) {
                    $filterOptions['min_range'] = $min;
                }
                if ($max !== null) {
                    $filterOptions['max_range'] = $max;
                }
                
                if (!empty($filterOptions)) {
                    return filter_var($input, FILTER_VALIDATE_INT, [
                        'options' => $filterOptions
                    ]) !== false;
                }
                
                return filter_var($input, FILTER_VALIDATE_INT) !== false;
                
            case 'float':
                return filter_var($input, FILTER_VALIDATE_FLOAT) !== false;
                
            case 'length':
                $min = $options['min'] ?? 0;
                $max = $options['max'] ?? PHP_INT_MAX;
                $length = strlen($input);
                return $length >= $min && $length <= $max;
                
            case 'regex':
                $pattern = $options['pattern'] ?? '';
                return !empty($pattern) && preg_match($pattern, $input);
                
            default:
                return true;
        }
    }
    
    /**
     * Rate Limiting
     */
    public static function checkRateLimit($action, $limit = 10, $window = 300) {
        $sessionManager = WiddxSessionManager::getInstance();
        $ip = self::getClientIP();
        $key = "rate_limit_{$action}_{$ip}";
        
        $attempts = $sessionManager->get($key, []);
        $now = time();
        
        // Remove old attempts outside the window
        $attempts = array_filter($attempts, function($timestamp) use ($now, $window) {
            return ($now - $timestamp) < $window;
        });
        
        // Check if limit exceeded
        if (count($attempts) >= $limit) {
            WiddxErrorHandler::logSecurityEvent('Rate limit exceeded', [
                'action' => $action,
                'ip' => $ip,
                'attempts' => count($attempts),
                'limit' => $limit,
                'window' => $window
            ]);
            return false;
        }
        
        // Add current attempt
        $attempts[] = $now;
        $sessionManager->set($key, $attempts);
        
        return true;
    }
    
    /**
     * Get Client IP Address
     */
    public static function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_CLIENT_IP',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ips = explode(',', $ip);
                    $ip = trim($ips[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Check for suspicious activity
     */
    public static function detectSuspiciousActivity($input) {
        $suspiciousPatterns = [
            // SQL Injection patterns
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
            
            // XSS patterns
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            
            // Path traversal
            '/\.\.[\/\\\\]/',
            
            // Command injection
            '/[;&|`$(){}[\]]/i',
            
            // File inclusion
            '/(include|require|file_get_contents|fopen|readfile)\s*\(/i',
            
            // PHP code injection
            '/<\?php/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec\s*\(/i',
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                WiddxErrorHandler::logSecurityEvent('Suspicious input detected', [
                    'pattern' => $pattern,
                    'input' => substr($input, 0, 200), // Log first 200 chars only
                    'ip' => self::getClientIP(),
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Generate secure random token
     */
    public static function generateSecureToken($length = 32) {
        try {
            return bin2hex(random_bytes($length));
        } catch (Exception $e) {
            // Fallback to less secure method
            return hash('sha256', uniqid(mt_rand(), true));
        }
    }
    
    /**
     * Hash password securely
     */
    public static function hashPassword($password) {
        return password_hash($password, PASSWORD_ARGON2ID, [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ]);
    }
    
    /**
     * Verify password
     */
    public static function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * Encrypt sensitive data
     */
    public static function encrypt($data, $key = null) {
        if ($key === null) {
            $key = self::getEncryptionKey();
        }
        
        $iv = random_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt sensitive data
     */
    public static function decrypt($encryptedData, $key = null) {
        if ($key === null) {
            $key = self::getEncryptionKey();
        }
        
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        
        return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
    }
    
    /**
     * Get encryption key (should be stored securely)
     */
    private static function getEncryptionKey() {
        // In production, this should be stored in environment variables or secure config
        return hash('sha256', 'WIDDX_ENCRYPTION_KEY_' . ($_SERVER['HTTP_HOST'] ?? 'localhost'));
    }
    
    /**
     * Check if request is from allowed origin
     */
    public static function checkOrigin($allowedOrigins = []) {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? $_SERVER['HTTP_REFERER'] ?? '';
        
        if (empty($origin)) {
            return true; // Allow requests without origin (direct access)
        }
        
        $parsedOrigin = parse_url($origin);
        $originHost = $parsedOrigin['host'] ?? '';
        
        // Default allowed origins
        if (empty($allowedOrigins)) {
            $allowedOrigins = [$_SERVER['HTTP_HOST'] ?? 'localhost'];
        }
        
        return in_array($originHost, $allowedOrigins);
    }
    
    /**
     * Initialize security measures
     */
    public static function initialize() {
        // Set security headers
        self::setSecurityHeaders();
        
        // Check for suspicious activity in all inputs
        $allInputs = array_merge($_GET, $_POST, $_COOKIE);
        foreach ($allInputs as $key => $value) {
            if (is_string($value) && self::detectSuspiciousActivity($value)) {
                // Log and potentially block the request
                http_response_code(403);
                die('Suspicious activity detected');
            }
        }
        
        // Check origin for POST requests
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && !self::checkOrigin()) {
            WiddxErrorHandler::logSecurityEvent('Invalid origin for POST request', [
                'origin' => $_SERVER['HTTP_ORIGIN'] ?? 'none',
                'referer' => $_SERVER['HTTP_REFERER'] ?? 'none'
            ]);
            http_response_code(403);
            die('Invalid request origin');
        }
    }
}

// Initialize security measures
WiddxSecurity::initialize();